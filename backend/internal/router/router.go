package router

import (
	"github.com/cloudwego/hertz/pkg/app/server"
	"gorm.io/gorm"

	"panel-backend/internal/app/handler"
	"panel-backend/internal/app/middleware"
	"panel-backend/internal/config"
)

// RegisterRoutes 注册所有路由
func RegisterRoutes(h *server.Hertz, db *gorm.DB, cfg *config.Config) {
	// 创建处理器
	authHandler := handler.NewAuthHandler(db, cfg)
	dashboardHandler := handler.NewDashboardHandler(db)
	containerHandler := handler.NewContainerHandler(db)
	
	// 全局中间件
	h.Use(middleware.CORS())
	h.Use(middleware.Logger())
	h.Use(middleware.Recovery())

	// API路由组
	api := h.Group("/api")
	
	// 公开路由（无需认证）
	public := api.Group("")
	{
		public.POST("/auth/login", authHandler.Login)
		public.GET("/health", func(c *server.Context) {
			c.JSO<PERSON>(200, map[string]interface{}{
				"status": "ok",
				"message": "Panel API is running",
			})
		})
	}

	// 需要认证的路由
	protected := api.Group("")
	protected.Use(middleware.JWTAuth(cfg.Security.JWTSecret))
	{
		// 认证相关
		auth := protected.Group("/auth")
		{
			auth.POST("/logout", authHandler.Logout)
			auth.POST("/refresh", authHandler.RefreshToken)
			auth.GET("/profile", authHandler.GetProfile)
			auth.PUT("/profile", authHandler.UpdateProfile)
			auth.POST("/change-password", authHandler.ChangePassword)
		}

		// 仪表板
		dashboard := protected.Group("/dashboard")
		{
			dashboard.GET("/overview", dashboardHandler.GetOverview)
			dashboard.GET("/stats", dashboardHandler.GetStats)
			dashboard.GET("/monitor", dashboardHandler.GetMonitorData)
		}

		// 容器管理
		containers := protected.Group("/containers")
		{
			containers.GET("", containerHandler.List)
			containers.POST("", containerHandler.Create)
			containers.GET("/:id", containerHandler.Get)
			containers.PUT("/:id", containerHandler.Update)
			containers.DELETE("/:id", containerHandler.Delete)
			containers.POST("/:id/start", containerHandler.Start)
			containers.POST("/:id/stop", containerHandler.Stop)
			containers.POST("/:id/restart", containerHandler.Restart)
			containers.GET("/:id/logs", containerHandler.GetLogs)
			containers.GET("/:id/stats", containerHandler.GetStats)
		}

		// 镜像管理
		images := protected.Group("/images")
		{
			images.GET("", containerHandler.ListImages)
			images.POST("", containerHandler.PullImage)
			images.DELETE("/:id", containerHandler.DeleteImage)
		}

		// 网络管理
		networks := protected.Group("/networks")
		{
			networks.GET("", containerHandler.ListNetworks)
			networks.POST("", containerHandler.CreateNetwork)
			networks.DELETE("/:id", containerHandler.DeleteNetwork)
		}

		// 卷管理
		volumes := protected.Group("/volumes")
		{
			volumes.GET("", containerHandler.ListVolumes)
			volumes.POST("", containerHandler.CreateVolume)
			volumes.DELETE("/:id", containerHandler.DeleteVolume)
		}
	}
}
