package middleware

import (
	"context"
	"fmt"
	"runtime"

	"github.com/cloudwego/hertz/pkg/app"
	"github.com/cloudwego/hertz/pkg/common/hlog"
)

// Recovery 恢复中间件
func Recovery() app.HandlerFunc {
	return func(ctx context.Context, c *app.RequestContext) {
		defer func() {
			if err := recover(); err != nil {
				// 获取堆栈信息
				buf := make([]byte, 2048)
				n := runtime.Stack(buf, false)
				stackTrace := string(buf[:n])
				
				// 记录错误日志
				hlog.Errorf("Panic recovered: %v\nStack trace:\n%s", err, stackTrace)
				
				// 返回错误响应
				c.J<PERSON>(500, map[string]interface{}{
					"code":    500,
					"message": "Internal server error",
					"error":   fmt.Sprintf("%v", err),
				})
				c.Abort()
			}
		}()
		
		c.Next(ctx)
	}
}
