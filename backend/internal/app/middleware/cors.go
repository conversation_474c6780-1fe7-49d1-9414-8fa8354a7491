package middleware

import (
	"context"

	"github.com/cloudwego/hertz/pkg/app"
)

// CORS 跨域中间件
func CORS() app.HandlerFunc {
	return func(ctx context.Context, c *app.RequestContext) {
		// 设置CORS头
		c.<PERSON>er("Access-Control-Allow-Origin", "*")
		c.<PERSON>("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, OPTIONS")
		c.<PERSON><PERSON>("Access-Control-Allow-Headers", "Content-Type, Authorization, X-Requested-With")
		c.<PERSON><PERSON>("Access-Control-Allow-Credentials", "true")
		c.<PERSON><PERSON>("Access-Control-Max-Age", "86400")

		// 处理预检请求
		if string(c.Method()) == "OPTIONS" {
			c.Status(204)
			return
		}

		c.Next(ctx)
	}
}
