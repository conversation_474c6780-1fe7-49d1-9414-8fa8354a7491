package middleware

import (
	"context"
	"time"

	"github.com/cloudwego/hertz/pkg/app"
	"github.com/cloudwego/hertz/pkg/common/hlog"
)

// Logger 日志中间件
func Logger() app.HandlerFunc {
	return func(ctx context.Context, c *app.RequestContext) {
		start := time.Now()
		
		c.Next(ctx)
		
		// 记录请求日志
		latency := time.Since(start)
		statusCode := c.Response.StatusCode()
		method := string(c.Method())
		path := string(c.Path())
		clientIP := c.ClientIP()
		userAgent := string(c.UserAgent())
		
		hlog.Infof("[%s] %s %s %d %v %s %s",
			time.Now().Format("2006-01-02 15:04:05"),
			method,
			path,
			statusCode,
			latency,
			clientIP,
			userAgent,
		)
	}
}
