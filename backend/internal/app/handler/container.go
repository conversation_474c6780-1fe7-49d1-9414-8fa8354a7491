package handler

import (
	"context"
	"strconv"

	"github.com/cloudwego/hertz/pkg/app"
	"gorm.io/gorm"

	"panel-backend/internal/domain/entity"
)

// ContainerHandler 容器处理器
type ContainerHandler struct {
	db *gorm.DB
}

// NewContainerHandler 创建容器处理器
func NewContainerHandler(db *gorm.DB) *ContainerHandler {
	return &ContainerHandler{
		db: db,
	}
}

// ListRequest 列表请求
type ListRequest struct {
	Page   int    `query:"page" validate:"min=1"`
	Size   int    `query:"size" validate:"min=1,max=100"`
	Status string `query:"status"`
	Search string `query:"search"`
}

// ListResponse 列表响应
type ListResponse struct {
	Items []interface{} `json:"items"`
	Total int64         `json:"total"`
	Page  int           `json:"page"`
	Size  int           `json:"size"`
	Pages int           `json:"pages"`
}

// List 获取容器列表
func (h *ContainerHandler) List(ctx context.Context, c *app.RequestContext) {
	var req ListRequest
	if err := c.BindAndValidate(&req); err != nil {
		c.JSON(400, map[string]interface{}{
			"code":    400,
			"message": "Invalid request parameters",
			"error":   err.Error(),
		})
		return
	}

	// 设置默认值
	if req.Page == 0 {
		req.Page = 1
	}
	if req.Size == 0 {
		req.Size = 20
	}

	// 构建查询
	query := h.db.Model(&entity.Container{})
	
	if req.Status != "" {
		query = query.Where("status = ?", req.Status)
	}
	
	if req.Search != "" {
		query = query.Where("name LIKE ? OR image LIKE ?", "%"+req.Search+"%", "%"+req.Search+"%")
	}

	// 获取总数
	var total int64
	query.Count(&total)

	// 分页查询
	var containers []entity.Container
	offset := (req.Page - 1) * req.Size
	query.Preload("Host").Offset(offset).Limit(req.Size).Find(&containers)

	// 转换为interface{}切片
	items := make([]interface{}, len(containers))
	for i, container := range containers {
		items[i] = container
	}

	// 计算总页数
	pages := int(total) / req.Size
	if int(total)%req.Size > 0 {
		pages++
	}

	response := ListResponse{
		Items: items,
		Total: total,
		Page:  req.Page,
		Size:  req.Size,
		Pages: pages,
	}

	c.JSON(200, map[string]interface{}{
		"code":    200,
		"message": "success",
		"data":    response,
	})
}

// CreateContainerRequest 创建容器请求
type CreateContainerRequest struct {
	Name        string `json:"name" validate:"required"`
	Image       string `json:"image" validate:"required"`
	Ports       string `json:"ports"`
	Volumes     string `json:"volumes"`
	Environment string `json:"environment"`
	Command     string `json:"command"`
	HostID      uint   `json:"host_id"`
}

// Create 创建容器
func (h *ContainerHandler) Create(ctx context.Context, c *app.RequestContext) {
	var req CreateContainerRequest
	if err := c.BindAndValidate(&req); err != nil {
		c.JSON(400, map[string]interface{}{
			"code":    400,
			"message": "Invalid request parameters",
			"error":   err.Error(),
		})
		return
	}

	// 创建容器记录
	container := entity.Container{
		ContainerID: "container_" + req.Name, // 实际应该从Docker API获取
		Name:        req.Name,
		Image:       req.Image,
		Status:      "created",
		Ports:       req.Ports,
		Volumes:     req.Volumes,
		Environment: req.Environment,
		Command:     req.Command,
		HostID:      req.HostID,
	}

	if err := h.db.Create(&container).Error; err != nil {
		c.JSON(500, map[string]interface{}{
			"code":    500,
			"message": "Failed to create container",
			"error":   err.Error(),
		})
		return
	}

	c.JSON(201, map[string]interface{}{
		"code":    201,
		"message": "Container created successfully",
		"data":    container,
	})
}

// Get 获取容器详情
func (h *ContainerHandler) Get(ctx context.Context, c *app.RequestContext) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		c.JSON(400, map[string]interface{}{
			"code":    400,
			"message": "Invalid container ID",
		})
		return
	}

	var container entity.Container
	if err := h.db.Preload("Host").First(&container, uint(id)).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			c.JSON(404, map[string]interface{}{
				"code":    404,
				"message": "Container not found",
			})
		} else {
			c.JSON(500, map[string]interface{}{
				"code":    500,
				"message": "Failed to get container",
			})
		}
		return
	}

	c.JSON(200, map[string]interface{}{
		"code":    200,
		"message": "success",
		"data":    container,
	})
}

// Update 更新容器
func (h *ContainerHandler) Update(ctx context.Context, c *app.RequestContext) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		c.JSON(400, map[string]interface{}{
			"code":    400,
			"message": "Invalid container ID",
		})
		return
	}

	var req CreateContainerRequest
	if err := c.BindAndValidate(&req); err != nil {
		c.JSON(400, map[string]interface{}{
			"code":    400,
			"message": "Invalid request parameters",
			"error":   err.Error(),
		})
		return
	}

	var container entity.Container
	if err := h.db.First(&container, uint(id)).Error; err != nil {
		c.JSON(404, map[string]interface{}{
			"code":    404,
			"message": "Container not found",
		})
		return
	}

	// 更新容器信息
	container.Name = req.Name
	container.Image = req.Image
	container.Ports = req.Ports
	container.Volumes = req.Volumes
	container.Environment = req.Environment
	container.Command = req.Command
	container.HostID = req.HostID

	if err := h.db.Save(&container).Error; err != nil {
		c.JSON(500, map[string]interface{}{
			"code":    500,
			"message": "Failed to update container",
		})
		return
	}

	c.JSON(200, map[string]interface{}{
		"code":    200,
		"message": "Container updated successfully",
		"data":    container,
	})
}

// Delete 删除容器
func (h *ContainerHandler) Delete(ctx context.Context, c *app.RequestContext) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		c.JSON(400, map[string]interface{}{
			"code":    400,
			"message": "Invalid container ID",
		})
		return
	}

	if err := h.db.Delete(&entity.Container{}, uint(id)).Error; err != nil {
		c.JSON(500, map[string]interface{}{
			"code":    500,
			"message": "Failed to delete container",
		})
		return
	}

	c.JSON(200, map[string]interface{}{
		"code":    200,
		"message": "Container deleted successfully",
	})
}

// Start 启动容器
func (h *ContainerHandler) Start(ctx context.Context, c *app.RequestContext) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		c.JSON(400, map[string]interface{}{
			"code":    400,
			"message": "Invalid container ID",
		})
		return
	}

	// 更新容器状态
	if err := h.db.Model(&entity.Container{}).Where("id = ?", uint(id)).Update("status", "running").Error; err != nil {
		c.JSON(500, map[string]interface{}{
			"code":    500,
			"message": "Failed to start container",
		})
		return
	}

	c.JSON(200, map[string]interface{}{
		"code":    200,
		"message": "Container started successfully",
	})
}

// Stop 停止容器
func (h *ContainerHandler) Stop(ctx context.Context, c *app.RequestContext) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		c.JSON(400, map[string]interface{}{
			"code":    400,
			"message": "Invalid container ID",
		})
		return
	}

	// 更新容器状态
	if err := h.db.Model(&entity.Container{}).Where("id = ?", uint(id)).Update("status", "stopped").Error; err != nil {
		c.JSON(500, map[string]interface{}{
			"code":    500,
			"message": "Failed to stop container",
		})
		return
	}

	c.JSON(200, map[string]interface{}{
		"code":    200,
		"message": "Container stopped successfully",
	})
}

// Restart 重启容器
func (h *ContainerHandler) Restart(ctx context.Context, c *app.RequestContext) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		c.JSON(400, map[string]interface{}{
			"code":    400,
			"message": "Invalid container ID",
		})
		return
	}

	// 更新容器状态
	if err := h.db.Model(&entity.Container{}).Where("id = ?", uint(id)).Update("status", "running").Error; err != nil {
		c.JSON(500, map[string]interface{}{
			"code":    500,
			"message": "Failed to restart container",
		})
		return
	}

	c.JSON(200, map[string]interface{}{
		"code":    200,
		"message": "Container restarted successfully",
	})
}

// GetLogs 获取容器日志
func (h *ContainerHandler) GetLogs(ctx context.Context, c *app.RequestContext) {
	idStr := c.Param("id")
	_, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		c.JSON(400, map[string]interface{}{
			"code":    400,
			"message": "Invalid container ID",
		})
		return
	}

	// 模拟容器日志
	logs := []string{
		"2024-01-01 10:00:00 Container started",
		"2024-01-01 10:00:01 Application initialized",
		"2024-01-01 10:00:02 Server listening on port 8080",
	}

	c.JSON(200, map[string]interface{}{
		"code":    200,
		"message": "success",
		"data":    logs,
	})
}

// GetStats 获取容器统计信息
func (h *ContainerHandler) GetStats(ctx context.Context, c *app.RequestContext) {
	idStr := c.Param("id")
	_, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		c.JSON(400, map[string]interface{}{
			"code":    400,
			"message": "Invalid container ID",
		})
		return
	}

	// 模拟容器统计信息
	stats := map[string]interface{}{
		"cpu_usage":    25.5,
		"memory_usage": 128 * 1024 * 1024, // 128MB
		"network_io": map[string]interface{}{
			"rx_bytes": 1024 * 100,
			"tx_bytes": 1024 * 50,
		},
		"block_io": map[string]interface{}{
			"read_bytes":  1024 * 1024 * 10, // 10MB
			"write_bytes": 1024 * 1024 * 5,  // 5MB
		},
	}

	c.JSON(200, map[string]interface{}{
		"code":    200,
		"message": "success",
		"data":    stats,
	})
}

// ListImages 获取镜像列表
func (h *ContainerHandler) ListImages(ctx context.Context, c *app.RequestContext) {
	var req ListRequest
	if err := c.BindAndValidate(&req); err != nil {
		c.JSON(400, map[string]interface{}{
			"code":    400,
			"message": "Invalid request parameters",
			"error":   err.Error(),
		})
		return
	}

	// 设置默认值
	if req.Page == 0 {
		req.Page = 1
	}
	if req.Size == 0 {
		req.Size = 20
	}

	// 构建查询
	query := h.db.Model(&entity.Image{})
	
	if req.Search != "" {
		query = query.Where("repository LIKE ? OR tag LIKE ?", "%"+req.Search+"%", "%"+req.Search+"%")
	}

	// 获取总数
	var total int64
	query.Count(&total)

	// 分页查询
	var images []entity.Image
	offset := (req.Page - 1) * req.Size
	query.Preload("Host").Offset(offset).Limit(req.Size).Find(&images)

	// 转换为interface{}切片
	items := make([]interface{}, len(images))
	for i, image := range images {
		items[i] = image
	}

	// 计算总页数
	pages := int(total) / req.Size
	if int(total)%req.Size > 0 {
		pages++
	}

	response := ListResponse{
		Items: items,
		Total: total,
		Page:  req.Page,
		Size:  req.Size,
		Pages: pages,
	}

	c.JSON(200, map[string]interface{}{
		"code":    200,
		"message": "success",
		"data":    response,
	})
}

// PullImageRequest 拉取镜像请求
type PullImageRequest struct {
	Repository string `json:"repository" validate:"required"`
	Tag        string `json:"tag"`
	HostID     uint   `json:"host_id"`
}

// PullImage 拉取镜像
func (h *ContainerHandler) PullImage(ctx context.Context, c *app.RequestContext) {
	var req PullImageRequest
	if err := c.BindAndValidate(&req); err != nil {
		c.JSON(400, map[string]interface{}{
			"code":    400,
			"message": "Invalid request parameters",
			"error":   err.Error(),
		})
		return
	}

	if req.Tag == "" {
		req.Tag = "latest"
	}

	// 创建镜像记录
	image := entity.Image{
		ImageID:    "image_" + req.Repository + "_" + req.Tag,
		Repository: req.Repository,
		Tag:        req.Tag,
		Size:       100 * 1024 * 1024, // 100MB
		HostID:     req.HostID,
	}

	if err := h.db.Create(&image).Error; err != nil {
		c.JSON(500, map[string]interface{}{
			"code":    500,
			"message": "Failed to pull image",
			"error":   err.Error(),
		})
		return
	}

	c.JSON(201, map[string]interface{}{
		"code":    201,
		"message": "Image pulled successfully",
		"data":    image,
	})
}

// DeleteImage 删除镜像
func (h *ContainerHandler) DeleteImage(ctx context.Context, c *app.RequestContext) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		c.JSON(400, map[string]interface{}{
			"code":    400,
			"message": "Invalid image ID",
		})
		return
	}

	if err := h.db.Delete(&entity.Image{}, uint(id)).Error; err != nil {
		c.JSON(500, map[string]interface{}{
			"code":    500,
			"message": "Failed to delete image",
		})
		return
	}

	c.JSON(200, map[string]interface{}{
		"code":    200,
		"message": "Image deleted successfully",
	})
}

// ListNetworks 获取网络列表
func (h *ContainerHandler) ListNetworks(ctx context.Context, c *app.RequestContext) {
	var networks []entity.Network
	h.db.Preload("Host").Find(&networks)

	c.JSON(200, map[string]interface{}{
		"code":    200,
		"message": "success",
		"data":    networks,
	})
}

// CreateNetworkRequest 创建网络请求
type CreateNetworkRequest struct {
	Name     string `json:"name" validate:"required"`
	Driver   string `json:"driver"`
	Internal bool   `json:"internal"`
	HostID   uint   `json:"host_id"`
}

// CreateNetwork 创建网络
func (h *ContainerHandler) CreateNetwork(ctx context.Context, c *app.RequestContext) {
	var req CreateNetworkRequest
	if err := c.BindAndValidate(&req); err != nil {
		c.JSON(400, map[string]interface{}{
			"code":    400,
			"message": "Invalid request parameters",
			"error":   err.Error(),
		})
		return
	}

	network := entity.Network{
		NetworkID: "network_" + req.Name,
		Name:      req.Name,
		Driver:    req.Driver,
		Internal:  req.Internal,
		HostID:    req.HostID,
	}

	if err := h.db.Create(&network).Error; err != nil {
		c.JSON(500, map[string]interface{}{
			"code":    500,
			"message": "Failed to create network",
			"error":   err.Error(),
		})
		return
	}

	c.JSON(201, map[string]interface{}{
		"code":    201,
		"message": "Network created successfully",
		"data":    network,
	})
}

// DeleteNetwork 删除网络
func (h *ContainerHandler) DeleteNetwork(ctx context.Context, c *app.RequestContext) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		c.JSON(400, map[string]interface{}{
			"code":    400,
			"message": "Invalid network ID",
		})
		return
	}

	if err := h.db.Delete(&entity.Network{}, uint(id)).Error; err != nil {
		c.JSON(500, map[string]interface{}{
			"code":    500,
			"message": "Failed to delete network",
		})
		return
	}

	c.JSON(200, map[string]interface{}{
		"code":    200,
		"message": "Network deleted successfully",
	})
}

// ListVolumes 获取卷列表
func (h *ContainerHandler) ListVolumes(ctx context.Context, c *app.RequestContext) {
	var volumes []entity.Volume
	h.db.Preload("Host").Find(&volumes)

	c.JSON(200, map[string]interface{}{
		"code":    200,
		"message": "success",
		"data":    volumes,
	})
}

// CreateVolumeRequest 创建卷请求
type CreateVolumeRequest struct {
	Name   string `json:"name" validate:"required"`
	Driver string `json:"driver"`
	HostID uint   `json:"host_id"`
}

// CreateVolume 创建卷
func (h *ContainerHandler) CreateVolume(ctx context.Context, c *app.RequestContext) {
	var req CreateVolumeRequest
	if err := c.BindAndValidate(&req); err != nil {
		c.JSON(400, map[string]interface{}{
			"code":    400,
			"message": "Invalid request parameters",
			"error":   err.Error(),
		})
		return
	}

	volume := entity.Volume{
		VolumeID: "volume_" + req.Name,
		Name:     req.Name,
		Driver:   req.Driver,
		HostID:   req.HostID,
	}

	if err := h.db.Create(&volume).Error; err != nil {
		c.JSON(500, map[string]interface{}{
			"code":    500,
			"message": "Failed to create volume",
			"error":   err.Error(),
		})
		return
	}

	c.JSON(201, map[string]interface{}{
		"code":    201,
		"message": "Volume created successfully",
		"data":    volume,
	})
}

// DeleteVolume 删除卷
func (h *ContainerHandler) DeleteVolume(ctx context.Context, c *app.RequestContext) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		c.JSON(400, map[string]interface{}{
			"code":    400,
			"message": "Invalid volume ID",
		})
		return
	}

	if err := h.db.Delete(&entity.Volume{}, uint(id)).Error; err != nil {
		c.JSON(500, map[string]interface{}{
			"code":    500,
			"message": "Failed to delete volume",
		})
		return
	}

	c.JSON(200, map[string]interface{}{
		"code":    200,
		"message": "Volume deleted successfully",
	})
}
