package handler

import (
	"context"
	"time"

	"github.com/cloudwego/hertz/pkg/app"
	"github.com/golang-jwt/jwt/v5"
	"golang.org/x/crypto/bcrypt"
	"gorm.io/gorm"

	"panel-backend/internal/app/middleware"
	"panel-backend/internal/config"
	"panel-backend/internal/domain/entity"
)

// AuthHandler 认证处理器
type AuthHandler struct {
	db  *gorm.DB
	cfg *config.Config
}

// NewAuthHandler 创建认证处理器
func NewAuthHandler(db *gorm.DB, cfg *config.Config) *AuthHandler {
	return &AuthHandler{
		db:  db,
		cfg: cfg,
	}
}

// LoginRequest 登录请求
type LoginRequest struct {
	Username string `json:"username" validate:"required"`
	Password string `json:"password" validate:"required"`
}

// LoginResponse 登录响应
type LoginResponse struct {
	Token     string      `json:"token"`
	ExpiresAt time.Time   `json:"expires_at"`
	User      *entity.User `json:"user"`
}

// Login 用户登录
func (h *AuthHandler) Login(ctx context.Context, c *app.RequestContext) {
	var req LoginRequest
	if err := c.BindAndValidate(&req); err != nil {
		c.JSON(400, map[string]interface{}{
			"code":    400,
			"message": "Invalid request parameters",
			"error":   err.Error(),
		})
		return
	}

	// 查找用户
	var user entity.User
	if err := h.db.Where("username = ?", req.Username).First(&user).Error; err != nil {
		c.JSON(401, map[string]interface{}{
			"code":    401,
			"message": "Invalid username or password",
		})
		return
	}

	// 验证密码
	if err := bcrypt.CompareHashAndPassword([]byte(user.Password), []byte(req.Password)); err != nil {
		c.JSON(401, map[string]interface{}{
			"code":    401,
			"message": "Invalid username or password",
		})
		return
	}

	// 检查用户状态
	if !user.IsActive() {
		c.JSON(403, map[string]interface{}{
			"code":    403,
			"message": "User account is not active",
		})
		return
	}

	// 生成JWT token
	expiresAt := time.Now().Add(h.cfg.Security.JWTExpiration)
	claims := &middleware.JWTClaims{
		UserID:   user.ID,
		Username: user.Username,
		Role:     user.Role,
		RegisteredClaims: jwt.RegisteredClaims{
			ExpiresAt: jwt.NewNumericDate(expiresAt),
			IssuedAt:  jwt.NewNumericDate(time.Now()),
		},
	}

	token := jwt.NewWithClaims(jwt.SigningMethodHS256, claims)
	tokenString, err := token.SignedString([]byte(h.cfg.Security.JWTSecret))
	if err != nil {
		c.JSON(500, map[string]interface{}{
			"code":    500,
			"message": "Failed to generate token",
		})
		return
	}

	// 更新最后登录时间
	now := time.Now()
	user.LastLoginAt = &now
	h.db.Save(&user)

	// 返回响应
	c.JSON(200, map[string]interface{}{
		"code":    200,
		"message": "Login successful",
		"data": LoginResponse{
			Token:     tokenString,
			ExpiresAt: expiresAt,
			User:      &user,
		},
	})
}

// Logout 用户登出
func (h *AuthHandler) Logout(ctx context.Context, c *app.RequestContext) {
	c.JSON(200, map[string]interface{}{
		"code":    200,
		"message": "Logout successful",
	})
}

// RefreshToken 刷新token
func (h *AuthHandler) RefreshToken(ctx context.Context, c *app.RequestContext) {
	userID, _ := c.Get("user_id")
	username, _ := c.Get("username")
	role, _ := c.Get("role")

	// 生成新的JWT token
	expiresAt := time.Now().Add(h.cfg.Security.JWTExpiration)
	claims := &middleware.JWTClaims{
		UserID:   userID.(uint),
		Username: username.(string),
		Role:     role.(string),
		RegisteredClaims: jwt.RegisteredClaims{
			ExpiresAt: jwt.NewNumericDate(expiresAt),
			IssuedAt:  jwt.NewNumericDate(time.Now()),
		},
	}

	token := jwt.NewWithClaims(jwt.SigningMethodHS256, claims)
	tokenString, err := token.SignedString([]byte(h.cfg.Security.JWTSecret))
	if err != nil {
		c.JSON(500, map[string]interface{}{
			"code":    500,
			"message": "Failed to generate token",
		})
		return
	}

	c.JSON(200, map[string]interface{}{
		"code":    200,
		"message": "Token refreshed successfully",
		"data": map[string]interface{}{
			"token":      tokenString,
			"expires_at": expiresAt,
		},
	})
}

// GetProfile 获取用户信息
func (h *AuthHandler) GetProfile(ctx context.Context, c *app.RequestContext) {
	userID, _ := c.Get("user_id")

	var user entity.User
	if err := h.db.First(&user, userID).Error; err != nil {
		c.JSON(404, map[string]interface{}{
			"code":    404,
			"message": "User not found",
		})
		return
	}

	c.JSON(200, map[string]interface{}{
		"code":    200,
		"message": "success",
		"data":    user,
	})
}

// UpdateProfileRequest 更新用户信息请求
type UpdateProfileRequest struct {
	Email string `json:"email" validate:"email"`
}

// UpdateProfile 更新用户信息
func (h *AuthHandler) UpdateProfile(ctx context.Context, c *app.RequestContext) {
	userID, _ := c.Get("user_id")

	var req UpdateProfileRequest
	if err := c.BindAndValidate(&req); err != nil {
		c.JSON(400, map[string]interface{}{
			"code":    400,
			"message": "Invalid request parameters",
			"error":   err.Error(),
		})
		return
	}

	var user entity.User
	if err := h.db.First(&user, userID).Error; err != nil {
		c.JSON(404, map[string]interface{}{
			"code":    404,
			"message": "User not found",
		})
		return
	}

	// 更新用户信息
	user.Email = req.Email
	if err := h.db.Save(&user).Error; err != nil {
		c.JSON(500, map[string]interface{}{
			"code":    500,
			"message": "Failed to update profile",
		})
		return
	}

	c.JSON(200, map[string]interface{}{
		"code":    200,
		"message": "Profile updated successfully",
		"data":    user,
	})
}

// ChangePasswordRequest 修改密码请求
type ChangePasswordRequest struct {
	OldPassword string `json:"old_password" validate:"required"`
	NewPassword string `json:"new_password" validate:"required,min=6"`
}

// ChangePassword 修改密码
func (h *AuthHandler) ChangePassword(ctx context.Context, c *app.RequestContext) {
	userID, _ := c.Get("user_id")

	var req ChangePasswordRequest
	if err := c.BindAndValidate(&req); err != nil {
		c.JSON(400, map[string]interface{}{
			"code":    400,
			"message": "Invalid request parameters",
			"error":   err.Error(),
		})
		return
	}

	var user entity.User
	if err := h.db.First(&user, userID).Error; err != nil {
		c.JSON(404, map[string]interface{}{
			"code":    404,
			"message": "User not found",
		})
		return
	}

	// 验证旧密码
	if err := bcrypt.CompareHashAndPassword([]byte(user.Password), []byte(req.OldPassword)); err != nil {
		c.JSON(400, map[string]interface{}{
			"code":    400,
			"message": "Invalid old password",
		})
		return
	}

	// 加密新密码
	hashedPassword, err := bcrypt.GenerateFromPassword([]byte(req.NewPassword), h.cfg.Security.BCryptCost)
	if err != nil {
		c.JSON(500, map[string]interface{}{
			"code":    500,
			"message": "Failed to encrypt password",
		})
		return
	}

	// 更新密码
	user.Password = string(hashedPassword)
	if err := h.db.Save(&user).Error; err != nil {
		c.JSON(500, map[string]interface{}{
			"code":    500,
			"message": "Failed to update password",
		})
		return
	}

	c.JSON(200, map[string]interface{}{
		"code":    200,
		"message": "Password changed successfully",
	})
}
