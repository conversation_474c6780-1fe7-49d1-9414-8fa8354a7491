package handler

import (
	"context"
	"runtime"

	"github.com/cloudwego/hertz/pkg/app"
	"gorm.io/gorm"

	"panel-backend/internal/domain/entity"
)

// DashboardHandler 仪表板处理器
type DashboardHandler struct {
	db *gorm.DB
}

// NewDashboardHandler 创建仪表板处理器
func NewDashboardHandler(db *gorm.DB) *DashboardHandler {
	return &DashboardHandler{
		db: db,
	}
}

// OverviewData 概览数据
type OverviewData struct {
	SystemInfo    SystemInfo    `json:"system_info"`
	ResourceStats ResourceStats `json:"resource_stats"`
	ServiceStats  ServiceStats  `json:"service_stats"`
}

// SystemInfo 系统信息
type SystemInfo struct {
	OS           string `json:"os"`
	Arch         string `json:"arch"`
	GoVersion    string `json:"go_version"`
	NumCPU       int    `json:"num_cpu"`
	NumGoroutine int    `json:"num_goroutine"`
}

// ResourceStats 资源统计
type ResourceStats struct {
	CPUUsage    float64 `json:"cpu_usage"`
	MemoryUsage float64 `json:"memory_usage"`
	DiskUsage   float64 `json:"disk_usage"`
	NetworkIO   NetworkIO `json:"network_io"`
}

// NetworkIO 网络IO
type NetworkIO struct {
	BytesReceived uint64 `json:"bytes_received"`
	BytesSent     uint64 `json:"bytes_sent"`
}

// ServiceStats 服务统计
type ServiceStats struct {
	ContainerCount int64 `json:"container_count"`
	ImageCount     int64 `json:"image_count"`
	NetworkCount   int64 `json:"network_count"`
	VolumeCount    int64 `json:"volume_count"`
	DatabaseCount  int64 `json:"database_count"`
	WebsiteCount   int64 `json:"website_count"`
}

// GetOverview 获取系统概览
func (h *DashboardHandler) GetOverview(ctx context.Context, c *app.RequestContext) {
	// 获取系统信息
	systemInfo := SystemInfo{
		OS:           runtime.GOOS,
		Arch:         runtime.GOARCH,
		GoVersion:    runtime.Version(),
		NumCPU:       runtime.NumCPU(),
		NumGoroutine: runtime.NumGoroutine(),
	}

	// 获取资源统计（模拟数据，实际应该从系统获取）
	resourceStats := ResourceStats{
		CPUUsage:    25.5,
		MemoryUsage: 68.2,
		DiskUsage:   45.8,
		NetworkIO: NetworkIO{
			BytesReceived: 1024 * 1024 * 100, // 100MB
			BytesSent:     1024 * 1024 * 50,  // 50MB
		},
	}

	// 获取服务统计
	var serviceStats ServiceStats
	h.db.Model(&entity.Container{}).Count(&serviceStats.ContainerCount)
	h.db.Model(&entity.Image{}).Count(&serviceStats.ImageCount)
	h.db.Model(&entity.Network{}).Count(&serviceStats.NetworkCount)
	h.db.Model(&entity.Volume{}).Count(&serviceStats.VolumeCount)
	h.db.Model(&entity.Database{}).Count(&serviceStats.DatabaseCount)
	h.db.Model(&entity.Website{}).Count(&serviceStats.WebsiteCount)

	overview := OverviewData{
		SystemInfo:    systemInfo,
		ResourceStats: resourceStats,
		ServiceStats:  serviceStats,
	}

	c.JSON(200, map[string]interface{}{
		"code":    200,
		"message": "success",
		"data":    overview,
	})
}

// StatsData 统计数据
type StatsData struct {
	TotalUsers       int64 `json:"total_users"`
	ActiveContainers int64 `json:"active_containers"`
	TotalImages      int64 `json:"total_images"`
	TotalWebsites    int64 `json:"total_websites"`
	TotalDatabases   int64 `json:"total_databases"`
	TotalTasks       int64 `json:"total_tasks"`
	CompletedTasks   int64 `json:"completed_tasks"`
	FailedTasks      int64 `json:"failed_tasks"`
}

// GetStats 获取统计数据
func (h *DashboardHandler) GetStats(ctx context.Context, c *app.RequestContext) {
	var stats StatsData

	// 获取各种统计数据
	h.db.Model(&entity.User{}).Count(&stats.TotalUsers)
	h.db.Model(&entity.Container{}).Where("status = ?", "running").Count(&stats.ActiveContainers)
	h.db.Model(&entity.Image{}).Count(&stats.TotalImages)
	h.db.Model(&entity.Website{}).Count(&stats.TotalWebsites)
	h.db.Model(&entity.Database{}).Count(&stats.TotalDatabases)
	h.db.Model(&entity.Task{}).Count(&stats.TotalTasks)
	h.db.Model(&entity.Task{}).Where("status = ?", "completed").Count(&stats.CompletedTasks)
	h.db.Model(&entity.Task{}).Where("status = ?", "failed").Count(&stats.FailedTasks)

	c.JSON(200, map[string]interface{}{
		"code":    200,
		"message": "success",
		"data":    stats,
	})
}

// MonitorData 监控数据
type MonitorData struct {
	Timestamp   int64   `json:"timestamp"`
	CPUUsage    float64 `json:"cpu_usage"`
	MemoryUsage float64 `json:"memory_usage"`
	DiskUsage   float64 `json:"disk_usage"`
	NetworkIn   uint64  `json:"network_in"`
	NetworkOut  uint64  `json:"network_out"`
}

// GetMonitorData 获取实时监控数据
func (h *DashboardHandler) GetMonitorData(ctx context.Context, c *app.RequestContext) {
	// 模拟实时监控数据
	// 实际应该从系统获取真实的监控数据
	monitorData := []MonitorData{
		{
			Timestamp:   1640995200, // 2022-01-01 00:00:00
			CPUUsage:    25.5,
			MemoryUsage: 68.2,
			DiskUsage:   45.8,
			NetworkIn:   1024 * 100,
			NetworkOut:  1024 * 50,
		},
		{
			Timestamp:   1640995260, // 2022-01-01 00:01:00
			CPUUsage:    28.3,
			MemoryUsage: 70.1,
			DiskUsage:   45.9,
			NetworkIn:   1024 * 120,
			NetworkOut:  1024 * 60,
		},
		// 可以添加更多数据点
	}

	c.JSON(200, map[string]interface{}{
		"code":    200,
		"message": "success",
		"data":    monitorData,
	})
}
