package entity

import (
	"time"
)

// Container 容器实体
type Container struct {
	BaseEntity
	ContainerID string `json:"container_id" gorm:"uniqueIndex;size:128;not null"`
	Name        string `json:"name" gorm:"size:128;not null"`
	Image       string `json:"image" gorm:"size:255;not null"`
	Status      string `json:"status" gorm:"size:32"`
	Ports       string `json:"ports" gorm:"type:text"`       // JSON格式存储端口映射
	Volumes     string `json:"volumes" gorm:"type:text"`     // JSON格式存储卷挂载
	Environment string `json:"environment" gorm:"type:text"` // JSON格式存储环境变量
	Command     string `json:"command" gorm:"type:text"`
	HostID      uint   `json:"host_id"`
	
	// 关联
	Host *Host `json:"host,omitempty" gorm:"foreignKey:HostID"`
}

// TableName 返回表名
func (Container) TableName() string {
	return "containers"
}

// Image 镜像实体
type Image struct {
	BaseEntity
	ImageID     string    `json:"image_id" gorm:"uniqueIndex;size:128;not null"`
	Repository  string    `json:"repository" gorm:"size:255;not null"`
	Tag         string    `json:"tag" gorm:"size:128;default:latest"`
	Size        int64     `json:"size"`
	CreatedTime time.Time `json:"created_time"`
	HostID      uint      `json:"host_id"`
	
	// 关联
	Host *Host `json:"host,omitempty" gorm:"foreignKey:HostID"`
}

// TableName 返回表名
func (Image) TableName() string {
	return "images"
}

// Network 网络实体
type Network struct {
	BaseEntity
	NetworkID string `json:"network_id" gorm:"uniqueIndex;size:128;not null"`
	Name      string `json:"name" gorm:"size:128;not null"`
	Driver    string `json:"driver" gorm:"size:64"`
	Scope     string `json:"scope" gorm:"size:32"`
	Internal  bool   `json:"internal"`
	IPAM      string `json:"ipam" gorm:"type:text"` // JSON格式存储IPAM配置
	Options   string `json:"options" gorm:"type:text"` // JSON格式存储选项
	HostID    uint   `json:"host_id"`
	
	// 关联
	Host *Host `json:"host,omitempty" gorm:"foreignKey:HostID"`
}

// TableName 返回表名
func (Network) TableName() string {
	return "networks"
}

// Volume 卷实体
type Volume struct {
	BaseEntity
	VolumeID   string `json:"volume_id" gorm:"uniqueIndex;size:128;not null"`
	Name       string `json:"name" gorm:"size:128;not null"`
	Driver     string `json:"driver" gorm:"size:64"`
	Mountpoint string `json:"mountpoint" gorm:"size:255"`
	Options    string `json:"options" gorm:"type:text"` // JSON格式存储选项
	Labels     string `json:"labels" gorm:"type:text"`  // JSON格式存储标签
	HostID     uint   `json:"host_id"`
	
	// 关联
	Host *Host `json:"host,omitempty" gorm:"foreignKey:HostID"`
}

// TableName 返回表名
func (Volume) TableName() string {
	return "volumes"
}
