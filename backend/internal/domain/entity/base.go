package entity

import (
	"time"

	"gorm.io/gorm"
)

// BaseEntity 基础实体，包含通用字段
type BaseEntity struct {
	ID        uint           `json:"id" gorm:"primarykey"`
	CreatedAt time.Time      `json:"created_at"`
	UpdatedAt time.Time      `json:"updated_at"`
	DeletedAt gorm.DeletedAt `json:"-" gorm:"index"`
}

// TableName 返回表名前缀
func (BaseEntity) TableName() string {
	return ""
}

// BeforeCreate GORM钩子：创建前
func (b *BaseEntity) BeforeCreate(tx *gorm.DB) error {
	now := time.Now()
	b.CreatedAt = now
	b.UpdatedAt = now
	return nil
}

// BeforeUpdate GORM钩子：更新前
func (b *BaseEntity) BeforeUpdate(tx *gorm.DB) error {
	b.UpdatedAt = time.Now()
	return nil
}
