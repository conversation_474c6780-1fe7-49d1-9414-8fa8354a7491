package entity

import (
	"time"
)

// User 用户实体
type User struct {
	BaseEntity
	Username    string     `json:"username" gorm:"uniqueIndex;size:64;not null" validate:"required,min=3,max=64"`
	Password    string     `json:"-" gorm:"size:255;not null" validate:"required,min=6"`
	Email       string     `json:"email" gorm:"size:128" validate:"email"`
	Role        string     `json:"role" gorm:"size:32;default:user" validate:"oneof=admin user readonly"`
	Status      string     `json:"status" gorm:"size:32;default:active" validate:"oneof=active inactive locked"`
	LastLoginAt *time.Time `json:"last_login_at"`
}

// TableName 返回表名
func (User) TableName() string {
	return "users"
}

// IsAdmin 检查是否为管理员
func (u *User) IsAdmin() bool {
	return u.Role == "admin"
}

// IsActive 检查用户是否激活
func (u *User) IsActive() bool {
	return u.Status == "active"
}

// CanAccess 检查用户是否可以访问指定资源
func (u *User) CanAccess(resource string) bool {
	if u.Role == "admin" {
		return true
	}
	
	if u.Role == "readonly" {
		// 只读用户只能访问查看类接口
		readOnlyResources := []string{
			"dashboard", "monitor", "containers:read", 
			"databases:read", "files:read", "websites:read",
		}
		for _, r := range readOnlyResources {
			if r == resource {
				return true
			}
		}
		return false
	}
	
	// 普通用户可以访问大部分功能，但不能管理用户和系统设置
	restrictedResources := []string{
		"users", "settings:security", "settings:system",
	}
	for _, r := range restrictedResources {
		if r == resource {
			return false
		}
	}
	
	return true
}
