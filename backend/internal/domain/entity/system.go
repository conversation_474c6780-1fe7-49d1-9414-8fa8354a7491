package entity

// Setting 系统设置实体
type Setting struct {
	BaseEntity
	Key         string `json:"key" gorm:"uniqueIndex;size:128;not null"`
	Value       string `json:"value" gorm:"type:text"`
	Description string `json:"description" gorm:"type:text"`
}

// TableName 返回表名
func (Setting) TableName() string {
	return "settings"
}

// Host 主机实体
type Host struct {
	BaseEntity
	Name        string `json:"name" gorm:"size:128;not null"`
	Address     string `json:"address" gorm:"size:128;not null"`
	Port        int    `json:"port" gorm:"default:22"`
	Username    string `json:"username" gorm:"size:64"`
	AuthMethod  string `json:"auth_method" gorm:"size:32;default:password"` // password, key
	Password    string `json:"-" gorm:"size:255"`
	PrivateKey  string `json:"-" gorm:"type:text"`
	GroupID     uint   `json:"group_id"`
	Status      string `json:"status" gorm:"size:32;default:active"`
	Description string `json:"description" gorm:"type:text"`
	
	// 关联
	Group      *HostGroup   `json:"group,omitempty" gorm:"foreignKey:GroupID"`
	Containers []Container  `json:"containers,omitempty" gorm:"foreignKey:HostID"`
	Images     []Image      `json:"images,omitempty" gorm:"foreignKey:HostID"`
}

// TableName 返回表名
func (Host) TableName() string {
	return "hosts"
}

// HostGroup 主机分组实体
type HostGroup struct {
	BaseEntity
	Name        string `json:"name" gorm:"uniqueIndex;size:128;not null"`
	Description string `json:"description" gorm:"type:text"`
	
	// 关联
	Hosts []Host `json:"hosts,omitempty" gorm:"foreignKey:GroupID"`
}

// TableName 返回表名
func (HostGroup) TableName() string {
	return "host_groups"
}

// SystemLog 系统日志实体
type SystemLog struct {
	BaseEntity
	Level   string `json:"level" gorm:"size:32;not null"`
	Message string `json:"message" gorm:"type:text;not null"`
	Module  string `json:"module" gorm:"size:64"`
	UserID  uint   `json:"user_id"`
	IP      string `json:"ip" gorm:"size:45"`
	
	// 关联
	User *User `json:"user,omitempty" gorm:"foreignKey:UserID"`
}

// TableName 返回表名
func (SystemLog) TableName() string {
	return "system_logs"
}

// OperationLog 操作日志实体
type OperationLog struct {
	BaseEntity
	Action     string `json:"action" gorm:"size:64;not null"`
	Resource   string `json:"resource" gorm:"size:64;not null"`
	ResourceID uint   `json:"resource_id"`
	Details    string `json:"details" gorm:"type:text"`
	UserID     uint   `json:"user_id;not null"`
	IP         string `json:"ip" gorm:"size:45"`
	UserAgent  string `json:"user_agent" gorm:"type:text"`
	
	// 关联
	User *User `json:"user,omitempty" gorm:"foreignKey:UserID"`
}

// TableName 返回表名
func (OperationLog) TableName() string {
	return "operation_logs"
}
