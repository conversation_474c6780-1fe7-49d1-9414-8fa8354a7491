package entity

// Database 数据库实体
type Database struct {
	BaseEntity
	Name        string `json:"name" gorm:"size:128;not null"`
	Type        string `json:"type" gorm:"size:32;not null"` // mysql, postgresql, redis, mongodb
	Host        string `json:"host" gorm:"size:128;not null"`
	Port        int    `json:"port" gorm:"not null"`
	Username    string `json:"username" gorm:"size:64"`
	Password    string `json:"-" gorm:"size:255"`
	DatabaseName string `json:"database_name" gorm:"size:128"`
	Charset     string `json:"charset" gorm:"size:32"`
	Collation   string `json:"collation" gorm:"size:64"`
	Status      string `json:"status" gorm:"size:32;default:active"`
	Description string `json:"description" gorm:"type:text"`
	
	// 关联
	Users []DatabaseUser `json:"users,omitempty" gorm:"foreignKey:DatabaseID"`
}

// TableName 返回表名
func (Database) TableName() string {
	return "databases"
}

// DatabaseUser 数据库用户实体
type DatabaseUser struct {
	BaseEntity
	DatabaseID uint   `json:"database_id;not null"`
	Username   string `json:"username" gorm:"size:64;not null"`
	Password   string `json:"-" gorm:"size:255;not null"`
	Host       string `json:"host" gorm:"size:128;default:%"`
	Privileges string `json:"privileges" gorm:"type:text"` // JSON格式存储权限
	
	// 关联
	Database *Database `json:"database,omitempty" gorm:"foreignKey:DatabaseID"`
}

// TableName 返回表名
func (DatabaseUser) TableName() string {
	return "database_users"
}

// Website 网站实体
type Website struct {
	BaseEntity
	Name        string `json:"name" gorm:"size:128;not null"`
	PrimaryDomain string `json:"primary_domain" gorm:"size:255;not null"`
	DocumentRoot  string `json:"document_root" gorm:"size:255;not null"`
	PHPVersion    string `json:"php_version" gorm:"size:16"`
	Status        string `json:"status" gorm:"size:32;default:active"`
	SSLEnabled    bool   `json:"ssl_enabled" gorm:"default:false"`
	SSLCertID     uint   `json:"ssl_cert_id"`
	Description   string `json:"description" gorm:"type:text"`
	
	// 关联
	Domains       []Domain         `json:"domains,omitempty" gorm:"foreignKey:WebsiteID"`
	SSLCertificate *SSLCertificate `json:"ssl_certificate,omitempty" gorm:"foreignKey:SSLCertID"`
}

// TableName 返回表名
func (Website) TableName() string {
	return "websites"
}

// Domain 域名实体
type Domain struct {
	BaseEntity
	WebsiteID uint   `json:"website_id;not null"`
	Domain    string `json:"domain" gorm:"uniqueIndex;size:255;not null"`
	Type      string `json:"type" gorm:"size:32;default:alias"` // primary, alias, redirect
	Target    string `json:"target" gorm:"size:255"`            // 重定向目标
	Status    string `json:"status" gorm:"size:32;default:active"`
	
	// 关联
	Website *Website `json:"website,omitempty" gorm:"foreignKey:WebsiteID"`
}

// TableName 返回表名
func (Domain) TableName() string {
	return "domains"
}

// SSLCertificate SSL证书实体
type SSLCertificate struct {
	BaseEntity
	Name        string `json:"name" gorm:"size:128;not null"`
	Domain      string `json:"domain" gorm:"size:255;not null"`
	Type        string `json:"type" gorm:"size:32;not null"` // self_signed, lets_encrypt, custom
	Certificate string `json:"-" gorm:"type:text"`           // 证书内容
	PrivateKey  string `json:"-" gorm:"type:text"`           // 私钥内容
	ExpiresAt   string `json:"expires_at"`
	Status      string `json:"status" gorm:"size:32;default:active"`
	AutoRenew   bool   `json:"auto_renew" gorm:"default:false"`
	
	// 关联
	Websites []Website `json:"websites,omitempty" gorm:"foreignKey:SSLCertID"`
}

// TableName 返回表名
func (SSLCertificate) TableName() string {
	return "ssl_certificates"
}
