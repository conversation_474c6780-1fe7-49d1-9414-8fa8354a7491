package entity

// Application 应用实体
type Application struct {
	BaseEntity
	Name           string `json:"name" gorm:"size:128;not null"`
	Key            string `json:"key" gorm:"uniqueIndex;size:128;not null"`
	Version        string `json:"version" gorm:"size:32"`
	Category       string `json:"category" gorm:"size:64"`
	Description    string `json:"description" gorm:"type:text"`
	Icon           string `json:"icon" gorm:"size:255"`
	DockerCompose  string `json:"docker_compose" gorm:"type:text"`
	InstallParams  string `json:"install_params" gorm:"type:text"` // JSON格式存储安装参数
	Status         string `json:"status" gorm:"size:32;default:available"`
	
	// 关联
	Instances []ApplicationInstance `json:"instances,omitempty" gorm:"foreignKey:ApplicationID"`
}

// TableName 返回表名
func (Application) TableName() string {
	return "applications"
}

// ApplicationInstance 应用实例实体
type ApplicationInstance struct {
	BaseEntity
	ApplicationID uint   `json:"application_id;not null"`
	Name          string `json:"name" gorm:"size:128;not null"`
	Version       string `json:"version" gorm:"size:32"`
	Status        string `json:"status" gorm:"size:32;default:installing"`
	InstallPath   string `json:"install_path" gorm:"size:255"`
	Config        string `json:"config" gorm:"type:text"`        // JSON格式存储配置
	Environment   string `json:"environment" gorm:"type:text"`   // JSON格式存储环境变量
	Ports         string `json:"ports" gorm:"type:text"`         // JSON格式存储端口映射
	
	// 关联
	Application *Application `json:"application,omitempty" gorm:"foreignKey:ApplicationID"`
}

// TableName 返回表名
func (ApplicationInstance) TableName() string {
	return "application_instances"
}

// Task 任务实体
type Task struct {
	BaseEntity
	Name        string `json:"name" gorm:"size:128;not null"`
	Type        string `json:"type" gorm:"size:64;not null"`
	Status      string `json:"status" gorm:"size:32;default:pending"`
	Progress    int    `json:"progress" gorm:"default:0"`
	Message     string `json:"message" gorm:"type:text"`
	Params      string `json:"params" gorm:"type:text"`      // JSON格式存储任务参数
	Result      string `json:"result" gorm:"type:text"`      // JSON格式存储任务结果
	UserID      uint   `json:"user_id;not null"`
	StartedAt   string `json:"started_at"`
	CompletedAt string `json:"completed_at"`
	
	// 关联
	User *User     `json:"user,omitempty" gorm:"foreignKey:UserID"`
	Logs []TaskLog `json:"logs,omitempty" gorm:"foreignKey:TaskID"`
}

// TableName 返回表名
func (Task) TableName() string {
	return "tasks"
}

// TaskLog 任务日志实体
type TaskLog struct {
	BaseEntity
	TaskID  uint   `json:"task_id;not null"`
	Level   string `json:"level" gorm:"size:32;not null"`
	Message string `json:"message" gorm:"type:text;not null"`
	
	// 关联
	Task *Task `json:"task,omitempty" gorm:"foreignKey:TaskID"`
}

// TableName 返回表名
func (TaskLog) TableName() string {
	return "task_logs"
}
