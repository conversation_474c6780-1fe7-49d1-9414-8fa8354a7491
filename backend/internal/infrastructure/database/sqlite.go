package database

import (
	"fmt"
	"os"
	"path/filepath"

	"gorm.io/driver/sqlite"
	"gorm.io/gorm"
	"gorm.io/gorm/logger"

	"panel-backend/internal/domain/entity"
)

// NewSQLiteDB 创建SQLite数据库连接
func NewSQLiteDB(dsn string) (*gorm.DB, error) {
	// 确保数据目录存在
	dir := filepath.Dir(dsn)
	if err := os.MkdirAll(dir, 0755); err != nil {
		return nil, fmt.Errorf("failed to create data directory: %w", err)
	}

	// 配置GORM
	config := &gorm.Config{
		Logger: logger.Default.LogMode(logger.Info),
	}

	// 连接数据库
	db, err := gorm.Open(sqlite.Open(dsn), config)
	if err != nil {
		return nil, fmt.Errorf("failed to connect to database: %w", err)
	}

	// 配置连接池
	sqlDB, err := db.DB()
	if err != nil {
		return nil, fmt.Errorf("failed to get underlying sql.DB: %w", err)
	}

	sqlDB.SetMaxOpenConns(25)
	sqlDB.SetMaxIdleConns(5)

	return db, nil
}

// Migrate 执行数据库迁移
func Migrate(db *gorm.DB) error {
	// 自动迁移所有实体
	entities := []interface{}{
		&entity.User{},
		&entity.Setting{},
		&entity.Host{},
		&entity.HostGroup{},
		&entity.Container{},
		&entity.Image{},
		&entity.Network{},
		&entity.Volume{},
		&entity.Database{},
		&entity.DatabaseUser{},
		&entity.Website{},
		&entity.Domain{},
		&entity.SSLCertificate{},
		&entity.Application{},
		&entity.ApplicationInstance{},
		&entity.Task{},
		&entity.TaskLog{},
		&entity.SystemLog{},
		&entity.OperationLog{},
	}

	for _, entity := range entities {
		if err := db.AutoMigrate(entity); err != nil {
			return fmt.Errorf("failed to migrate %T: %w", entity, err)
		}
	}

	// 创建默认管理员用户
	if err := createDefaultAdmin(db); err != nil {
		return fmt.Errorf("failed to create default admin: %w", err)
	}

	return nil
}

// createDefaultAdmin 创建默认管理员用户
func createDefaultAdmin(db *gorm.DB) error {
	var count int64
	if err := db.Model(&entity.User{}).Count(&count); err != nil {
		return err
	}

	// 如果已有用户，跳过创建
	if count > 0 {
		return nil
	}

	// 创建默认管理员
	admin := &entity.User{
		Username: "admin",
		Password: "$2a$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj8xLpQy3C2G", // password: admin123
		Email:    "<EMAIL>",
		Role:     "admin",
		Status:   "active",
	}

	return db.Create(admin).Error
}
