package main

import (
	"log"
	"os"
	"os/signal"
	"syscall"

	"github.com/cloudwego/hertz/pkg/app/server"
	"github.com/cloudwego/hertz/pkg/common/hlog"

	"panel-backend/internal/config"
	"panel-backend/internal/infrastructure/database"
	"panel-backend/internal/router"
)

func main() {
	// 加载配置
	cfg, err := config.Load()
	if err != nil {
		log.Fatalf("Failed to load config: %v", err)
	}

	// 初始化数据库
	db, err := database.NewSQLiteDB(cfg.Database.DSN)
	if err != nil {
		log.Fatalf("Failed to connect to database: %v", err)
	}

	// 运行数据库迁移
	if err := database.Migrate(db); err != nil {
		log.Fatalf("Failed to migrate database: %v", err)
	}

	// 创建Hertz服务器
	h := server.Default(
		server.WithHostPorts(cfg.Server.Address),
		server.WithMaxRequestBodySize(cfg.Server.MaxRequestBodySize),
	)

	// 注册路由
	router.RegisterRoutes(h, db, cfg)

	// 启动服务器
	hlog.Infof("Server starting on %s", cfg.Server.Address)

	// 优雅关闭
	go func() {
		h.Spin()
	}()

	// 等待中断信号
	quit := make(chan os.Signal, 1)
	signal.Notify(quit, syscall.SIGINT, syscall.SIGTERM)
	<-quit

	hlog.Info("Server shutting down...")

	// 关闭数据库连接
	sqlDB, err := db.DB()
	if err == nil {
		sqlDB.Close()
	}

	hlog.Info("Server stopped")
}
