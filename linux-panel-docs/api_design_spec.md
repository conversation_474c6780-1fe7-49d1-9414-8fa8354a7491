# API设计规范

## 1. 基础规范

### 1.1 URL设计
- 基础路径：`/api`
- 资源命名：使用复数名词，如 `/api/containers`
- 层级关系：`/api/websites/{id}/ssl`
- 查询参数：`/api/containers?status=running&page=1&size=20`

### 1.2 HTTP方法
- `GET`：获取资源
- `POST`：创建资源
- `PUT`：完整更新资源
- `PATCH`：部分更新资源
- `DELETE`：删除资源

### 1.3 状态码
- `200`：成功
- `201`：创建成功
- `204`：删除成功
- `400`：请求参数错误
- `401`：未认证
- `403`：无权限
- `404`：资源不存在
- `409`：资源冲突
- `500`：服务器内部错误

## 2. 请求响应格式

### 2.1 统一响应格式
```json
{
  "code": 200,
  "message": "success",
  "data": {},
  "timestamp": "2024-01-01T00:00:00Z"
}
```

### 2.2 分页响应格式
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "items": [],
    "total": 100,
    "page": 1,
    "size": 20,
    "pages": 5
  },
  "timestamp": "2024-01-01T00:00:00Z"
}
```

### 2.3 错误响应格式
```json
{
  "code": 400,
  "message": "参数验证失败",
  "error": {
    "type": "validation_error",
    "details": [
      {
        "field": "name",
        "message": "名称不能为空"
      }
    ]
  },
  "timestamp": "2024-01-01T00:00:00Z"
}
```

## 3. 核心API接口

### 3.1 认证相关
```
POST   /api/auth/login          # 用户登录
POST   /api/auth/logout         # 用户登出
POST   /api/auth/refresh        # 刷新Token
GET    /api/auth/profile        # 获取用户信息
PUT    /api/auth/profile        # 更新用户信息
POST   /api/auth/change-password # 修改密码
```

### 3.2 仪表板
```
GET    /api/dashboard/overview  # 系统概览
GET    /api/dashboard/stats     # 统计数据
GET    /api/dashboard/monitor   # 实时监控数据
```

### 3.3 容器管理
```
GET    /api/containers          # 获取容器列表
POST   /api/containers          # 创建容器
GET    /api/containers/{id}     # 获取容器详情
PUT    /api/containers/{id}     # 更新容器
DELETE /api/containers/{id}     # 删除容器
POST   /api/containers/{id}/start   # 启动容器
POST   /api/containers/{id}/stop    # 停止容器
POST   /api/containers/{id}/restart # 重启容器
GET    /api/containers/{id}/logs    # 获取容器日志
GET    /api/containers/{id}/stats   # 获取容器统计
```

### 3.4 镜像管理
```
GET    /api/images              # 获取镜像列表
POST   /api/images/pull         # 拉取镜像
POST   /api/images/build        # 构建镜像
DELETE /api/images/{id}         # 删除镜像
POST   /api/images/{id}/tag     # 标记镜像
```

### 3.5 数据库管理
```
GET    /api/databases           # 获取数据库实例列表
POST   /api/databases           # 创建数据库实例
GET    /api/databases/{id}      # 获取数据库详情
PUT    /api/databases/{id}      # 更新数据库配置
DELETE /api/databases/{id}      # 删除数据库实例

# MySQL相关
GET    /api/databases/{id}/mysql/databases    # 获取数据库列表
POST   /api/databases/{id}/mysql/databases    # 创建数据库
DELETE /api/databases/{id}/mysql/databases/{name} # 删除数据库
GET    /api/databases/{id}/mysql/users        # 获取用户列表
POST   /api/databases/{id}/mysql/users        # 创建用户
PUT    /api/databases/{id}/mysql/users/{user} # 更新用户权限
DELETE /api/databases/{id}/mysql/users/{user} # 删除用户
```

### 3.6 文件管理
```
GET    /api/files               # 获取文件列表
POST   /api/files               # 创建文件/目录
GET    /api/files/content       # 获取文件内容
PUT    /api/files/content       # 更新文件内容
DELETE /api/files               # 删除文件/目录
POST   /api/files/upload        # 上传文件
GET    /api/files/download      # 下载文件
POST   /api/files/compress      # 压缩文件
POST   /api/files/extract       # 解压文件
PUT    /api/files/permissions   # 修改权限
```

### 3.7 网站管理
```
GET    /api/websites            # 获取网站列表
POST   /api/websites            # 创建网站
GET    /api/websites/{id}       # 获取网站详情
PUT    /api/websites/{id}       # 更新网站配置
DELETE /api/websites/{id}       # 删除网站
POST   /api/websites/{id}/start # 启动网站
POST   /api/websites/{id}/stop  # 停止网站
GET    /api/websites/{id}/nginx # 获取Nginx配置
PUT    /api/websites/{id}/nginx # 更新Nginx配置
```

### 3.8 SSL证书管理
```
GET    /api/ssl                 # 获取证书列表
POST   /api/ssl                 # 创建/申请证书
GET    /api/ssl/{id}            # 获取证书详情
PUT    /api/ssl/{id}            # 更新证书
DELETE /api/ssl/{id}            # 删除证书
POST   /api/ssl/{id}/renew      # 续期证书
```

### 3.9 应用商店
```
GET    /api/apps                # 获取应用列表
GET    /api/apps/{key}          # 获取应用详情
POST   /api/apps/{key}/install  # 安装应用
GET    /api/apps/installed      # 获取已安装应用
PUT    /api/apps/installed/{id} # 更新应用配置
DELETE /api/apps/installed/{id} # 卸载应用
POST   /api/apps/installed/{id}/start   # 启动应用
POST   /api/apps/installed/{id}/stop    # 停止应用
POST   /api/apps/installed/{id}/restart # 重启应用
```

### 3.10 系统监控
```
GET    /api/monitor/system      # 获取系统信息
GET    /api/monitor/processes   # 获取进程列表
POST   /api/monitor/processes/{pid}/kill # 终止进程
GET    /api/monitor/network     # 获取网络信息
GET    /api/monitor/disk        # 获取磁盘信息
GET    /api/monitor/history     # 获取历史监控数据
```

### 3.11 定时任务
```
GET    /api/cronjobs            # 获取定时任务列表
POST   /api/cronjobs            # 创建定时任务
GET    /api/cronjobs/{id}       # 获取任务详情
PUT    /api/cronjobs/{id}       # 更新任务
DELETE /api/cronjobs/{id}       # 删除任务
POST   /api/cronjobs/{id}/run   # 手动执行任务
GET    /api/cronjobs/{id}/logs  # 获取执行日志
```

### 3.12 备份管理
```
GET    /api/backups             # 获取备份列表
POST   /api/backups             # 创建备份
GET    /api/backups/{id}        # 获取备份详情
DELETE /api/backups/{id}        # 删除备份
POST   /api/backups/{id}/restore # 恢复备份
GET    /api/backups/{id}/download # 下载备份文件
```

### 3.13 系统设置
```
GET    /api/settings            # 获取系统设置
PUT    /api/settings            # 更新系统设置
GET    /api/settings/security   # 获取安全设置
PUT    /api/settings/security   # 更新安全设置
POST   /api/settings/backup-test # 测试备份配置
```

## 4. 认证授权

### 4.1 JWT Token
- Header: `Authorization: Bearer <token>`
- Token过期时间：24小时
- 刷新Token过期时间：7天

### 4.2 权限控制
- 基于角色的访问控制(RBAC)
- 角色：admin, user, readonly
- 资源级别权限控制

## 5. 中间件

### 5.1 认证中间件
- JWT Token验证
- Session验证
- 用户状态检查

### 5.2 授权中间件
- 权限验证
- 资源访问控制
- 操作审计

### 5.3 日志中间件
- 请求日志记录
- 响应时间统计
- 错误日志记录

### 5.4 限流中间件
- IP限流
- 用户限流
- API限流

## 6. WebSocket接口

### 6.1 实时监控
```
WS /api/ws/monitor              # 实时系统监控数据
WS /api/ws/containers/{id}/logs # 实时容器日志
WS /api/ws/terminal             # Web终端
```

### 6.2 消息格式
```json
{
  "type": "monitor_data",
  "data": {
    "cpu": 45.2,
    "memory": 68.5,
    "disk": 32.1
  },
  "timestamp": "2024-01-01T00:00:00Z"
}
```
