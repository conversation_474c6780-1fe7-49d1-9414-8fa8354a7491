-- Linux运维管理面板数据库设计
-- 基于SQLite，使用GORM进行ORM映射

-- 用户表
CREATE TABLE users (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    username VARCHAR(64) NOT NULL UNIQUE,
    password VARCHAR(255) NOT NULL,
    email VARCHAR(128),
    role VARCHAR(32) DEFAULT 'user',
    status VARCHAR(32) DEFAULT 'active',
    last_login_at DATETIME,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- 系统设置表
CREATE TABLE settings (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    key VARCHAR(128) NOT NULL UNIQUE,
    value TEXT,
    description TEXT,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- 主机信息表
CREATE TABLE hosts (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    name VA<PERSON>HA<PERSON>(128) NOT NULL,
    address VARCHAR(128) NOT NULL,
    port INTEGER DEFAULT 22,
    username VARCHAR(64),
    auth_method VARCHAR(32) DEFAULT 'password', -- password, key
    password VARCHAR(255),
    private_key TEXT,
    group_id INTEGER,
    status VARCHAR(32) DEFAULT 'active',
    description TEXT,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- 主机分组表
CREATE TABLE host_groups (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    name VARCHAR(128) NOT NULL UNIQUE,
    description TEXT,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- 容器表
CREATE TABLE containers (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    container_id VARCHAR(128) NOT NULL UNIQUE,
    name VARCHAR(128) NOT NULL,
    image VARCHAR(255) NOT NULL,
    status VARCHAR(32),
    ports TEXT, -- JSON格式存储端口映射
    volumes TEXT, -- JSON格式存储卷挂载
    environment TEXT, -- JSON格式存储环境变量
    command TEXT,
    host_id INTEGER,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- 镜像表
CREATE TABLE images (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    image_id VARCHAR(128) NOT NULL UNIQUE,
    repository VARCHAR(255) NOT NULL,
    tag VARCHAR(128) DEFAULT 'latest',
    size BIGINT,
    created_time DATETIME,
    host_id INTEGER,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- 数据库实例表
CREATE TABLE databases (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    name VARCHAR(128) NOT NULL,
    type VARCHAR(32) NOT NULL, -- mysql, postgresql, redis
    version VARCHAR(32),
    host VARCHAR(128) DEFAULT 'localhost',
    port INTEGER,
    username VARCHAR(128),
    password VARCHAR(255),
    container_id VARCHAR(128),
    status VARCHAR(32) DEFAULT 'running',
    description TEXT,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- 数据库用户表
CREATE TABLE database_users (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    database_id INTEGER NOT NULL,
    username VARCHAR(128) NOT NULL,
    password VARCHAR(255) NOT NULL,
    privileges TEXT, -- JSON格式存储权限
    host VARCHAR(128) DEFAULT '%',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- 网站表
CREATE TABLE websites (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    name VARCHAR(128) NOT NULL UNIQUE,
    domain VARCHAR(255) NOT NULL,
    document_root VARCHAR(255),
    runtime_type VARCHAR(32), -- php, node, python, static
    runtime_version VARCHAR(32),
    ssl_enabled BOOLEAN DEFAULT FALSE,
    ssl_cert_id INTEGER,
    nginx_config TEXT,
    status VARCHAR(32) DEFAULT 'running',
    description TEXT,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- SSL证书表
CREATE TABLE ssl_certificates (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    name VARCHAR(128) NOT NULL,
    domain VARCHAR(255) NOT NULL,
    cert_content TEXT,
    key_content TEXT,
    ca_content TEXT,
    issuer VARCHAR(255),
    expire_date DATETIME,
    auto_renew BOOLEAN DEFAULT FALSE,
    status VARCHAR(32) DEFAULT 'valid',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- 应用表
CREATE TABLE applications (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    name VARCHAR(128) NOT NULL,
    key VARCHAR(128) NOT NULL UNIQUE,
    version VARCHAR(32),
    category VARCHAR(64),
    description TEXT,
    icon VARCHAR(255),
    docker_compose TEXT,
    install_params TEXT, -- JSON格式存储安装参数
    status VARCHAR(32) DEFAULT 'available',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- 应用安装记录表
CREATE TABLE app_installs (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    app_id INTEGER NOT NULL,
    name VARCHAR(128) NOT NULL,
    version VARCHAR(32),
    install_path VARCHAR(255),
    container_names TEXT, -- JSON格式存储容器名称
    port INTEGER,
    params TEXT, -- JSON格式存储安装参数
    status VARCHAR(32) DEFAULT 'running',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- 定时任务表
CREATE TABLE cronjobs (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    name VARCHAR(128) NOT NULL,
    type VARCHAR(32) NOT NULL, -- backup, script, command
    spec VARCHAR(128) NOT NULL, -- cron表达式
    command TEXT,
    script_path VARCHAR(255),
    backup_type VARCHAR(32),
    backup_target TEXT,
    status VARCHAR(32) DEFAULT 'active',
    last_run_at DATETIME,
    next_run_at DATETIME,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- 备份记录表
CREATE TABLE backups (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    name VARCHAR(128) NOT NULL,
    type VARCHAR(32) NOT NULL, -- database, website, system
    source VARCHAR(255) NOT NULL,
    file_path VARCHAR(255),
    file_size BIGINT,
    storage_type VARCHAR(32) DEFAULT 'local', -- local, s3, oss, cos
    storage_config TEXT, -- JSON格式存储存储配置
    status VARCHAR(32) DEFAULT 'completed',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- 操作日志表
CREATE TABLE operation_logs (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    user_id INTEGER,
    operation VARCHAR(128) NOT NULL,
    resource_type VARCHAR(64),
    resource_id INTEGER,
    method VARCHAR(16),
    path VARCHAR(255),
    ip VARCHAR(64),
    user_agent TEXT,
    request_data TEXT,
    response_data TEXT,
    status_code INTEGER,
    error_message TEXT,
    duration INTEGER, -- 执行时间(毫秒)
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- 系统监控数据表
CREATE TABLE monitor_data (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    host_id INTEGER,
    cpu_usage DECIMAL(5,2),
    memory_usage DECIMAL(5,2),
    disk_usage DECIMAL(5,2),
    network_in BIGINT,
    network_out BIGINT,
    load_average DECIMAL(5,2),
    recorded_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- 创建索引
CREATE INDEX idx_users_username ON users(username);
CREATE INDEX idx_settings_key ON settings(key);
CREATE INDEX idx_hosts_group_id ON hosts(group_id);
CREATE INDEX idx_containers_host_id ON containers(host_id);
CREATE INDEX idx_images_host_id ON images(host_id);
CREATE INDEX idx_databases_type ON databases(type);
CREATE INDEX idx_websites_domain ON websites(domain);
CREATE INDEX idx_operation_logs_user_id ON operation_logs(user_id);
CREATE INDEX idx_operation_logs_created_at ON operation_logs(created_at);
CREATE INDEX idx_monitor_data_host_id ON monitor_data(host_id);
CREATE INDEX idx_monitor_data_recorded_at ON monitor_data(recorded_at);
