# 后端技术栈实现方案

## 1. 核心框架选择

### 1.1 HTTP框架：Hertz
```go
// 选择理由：
// 1. 字节跳动开源，性能优异
// 2. 支持HTTP/1.1、HTTP/2、HTTP/3
// 3. 丰富的中间件生态
// 4. 良好的扩展性和可维护性

import (
    "github.com/cloudwego/hertz/pkg/app/server"
    "github.com/cloudwego/hertz/pkg/app"
    "github.com/cloudwego/hertz/pkg/protocol/consts"
)

func main() {
    h := server.Default()
    
    // 注册中间件
    h.Use(middleware.Logger())
    h.Use(middleware.Recovery())
    h.Use(middleware.CORS())
    
    // 注册路由
    registerRoutes(h)
    
    h.Spin()
}
```

### 1.2 ORM框架：GORM
```go
// 选择理由：
// 1. Go生态最成熟的ORM
// 2. 支持多种数据库
// 3. 丰富的查询功能
// 4. 良好的性能和缓存机制

import (
    "gorm.io/gorm"
    "gorm.io/driver/sqlite"
)

type Database struct {
    *gorm.DB
}

func NewDatabase() (*Database, error) {
    db, err := gorm.Open(sqlite.Open("ops_panel.db"), &gorm.Config{
        Logger: logger.Default.LogMode(logger.Info),
    })
    if err != nil {
        return nil, err
    }
    
    return &Database{DB: db}, nil
}
```

### 1.3 数据库：SQLite
```go
// 选择理由：
// 1. 轻量级，无需额外部署
// 2. 支持并发读写
// 3. 数据备份简单
// 4. 适合中小型运维面板

// 数据库配置
type Config struct {
    Database struct {
        Driver   string `yaml:"driver" default:"sqlite"`
        DSN      string `yaml:"dsn" default:"./data/ops_panel.db"`
        MaxIdle  int    `yaml:"max_idle" default:"10"`
        MaxOpen  int    `yaml:"max_open" default:"100"`
        LogLevel string `yaml:"log_level" default:"info"`
    } `yaml:"database"`
}
```

## 2. 项目架构设计

### 2.1 分层架构
```
┌─────────────────┐
│   Handler层     │  # HTTP请求处理
├─────────────────┤
│   Service层     │  # 业务逻辑处理
├─────────────────┤
│  Repository层   │  # 数据访问层
├─────────────────┤
│   Entity层      │  # 数据模型层
└─────────────────┘
```

### 2.2 依赖注入
```go
// 使用Wire进行依赖注入
//go:build wireinject
// +build wireinject

package main

import (
    "github.com/google/wire"
)

func InitializeApp() (*App, error) {
    wire.Build(
        // Database
        NewDatabase,
        
        // Repositories
        repository.NewUserRepository,
        repository.NewContainerRepository,
        repository.NewDatabaseRepository,
        
        // Services
        service.NewUserService,
        service.NewContainerService,
        service.NewDatabaseService,
        
        // Handlers
        handler.NewUserHandler,
        handler.NewContainerHandler,
        handler.NewDatabaseHandler,
        
        // App
        NewApp,
    )
    return &App{}, nil
}
```

## 3. 核心组件实现

### 3.1 配置管理
```go
package config

import (
    "github.com/spf13/viper"
)

type Config struct {
    Server   ServerConfig   `yaml:"server"`
    Database DatabaseConfig `yaml:"database"`
    Security SecurityConfig `yaml:"security"`
    Docker   DockerConfig   `yaml:"docker"`
}

type ServerConfig struct {
    Host         string `yaml:"host" default:"0.0.0.0"`
    Port         int    `yaml:"port" default:"8080"`
    ReadTimeout  int    `yaml:"read_timeout" default:"30"`
    WriteTimeout int    `yaml:"write_timeout" default:"30"`
}

func Load() (*Config, error) {
    viper.SetConfigName("config")
    viper.SetConfigType("yaml")
    viper.AddConfigPath("./configs")
    viper.AddConfigPath(".")
    
    if err := viper.ReadInConfig(); err != nil {
        return nil, err
    }
    
    var config Config
    if err := viper.Unmarshal(&config); err != nil {
        return nil, err
    }
    
    return &config, nil
}
```

### 3.2 日志管理
```go
package logger

import (
    "github.com/sirupsen/logrus"
    "gopkg.in/natefinch/lumberjack.v2"
)

type Logger struct {
    *logrus.Logger
}

func New() *Logger {
    log := logrus.New()
    
    // 设置日志格式
    log.SetFormatter(&logrus.JSONFormatter{
        TimestampFormat: "2006-01-02 15:04:05",
    })
    
    // 设置日志输出
    log.SetOutput(&lumberjack.Logger{
        Filename:   "./logs/app.log",
        MaxSize:    100, // MB
        MaxBackups: 3,
        MaxAge:     28, // days
        Compress:   true,
    })
    
    return &Logger{Logger: log}
}
```

### 3.3 中间件实现
```go
package middleware

import (
    "context"
    "time"
    
    "github.com/cloudwego/hertz/pkg/app"
)

// JWT认证中间件
func JWTAuth() app.HandlerFunc {
    return func(ctx context.Context, c *app.RequestContext) {
        token := string(c.GetHeader("Authorization"))
        if token == "" {
            c.JSON(401, gin.H{"error": "未提供认证令牌"})
            c.Abort()
            return
        }
        
        // 验证JWT Token
        claims, err := jwt.ParseToken(token)
        if err != nil {
            c.JSON(401, gin.H{"error": "无效的认证令牌"})
            c.Abort()
            return
        }
        
        c.Set("user_id", claims.UserID)
        c.Next(ctx)
    }
}

// 操作日志中间件
func OperationLog() app.HandlerFunc {
    return func(ctx context.Context, c *app.RequestContext) {
        start := time.Now()
        
        c.Next(ctx)
        
        // 记录操作日志
        duration := time.Since(start)
        logEntry := &entity.OperationLog{
            UserID:     getUserID(c),
            Method:     string(c.Method()),
            Path:       string(c.Path()),
            IP:         c.ClientIP(),
            UserAgent:  string(c.GetHeader("User-Agent")),
            StatusCode: c.Response.StatusCode(),
            Duration:   int(duration.Milliseconds()),
        }
        
        // 异步保存日志
        go saveOperationLog(logEntry)
    }
}

// 限流中间件
func RateLimit() app.HandlerFunc {
    return func(ctx context.Context, c *app.RequestContext) {
        ip := c.ClientIP()
        
        if !rateLimiter.Allow(ip) {
            c.JSON(429, gin.H{"error": "请求过于频繁"})
            c.Abort()
            return
        }
        
        c.Next(ctx)
    }
}
```

### 3.4 错误处理
```go
package errors

import (
    "fmt"
    "net/http"
)

type AppError struct {
    Code    int    `json:"code"`
    Message string `json:"message"`
    Type    string `json:"type"`
    Details interface{} `json:"details,omitempty"`
}

func (e *AppError) Error() string {
    return e.Message
}

// 预定义错误
var (
    ErrInvalidParams = &AppError{
        Code:    http.StatusBadRequest,
        Message: "参数验证失败",
        Type:    "validation_error",
    }
    
    ErrUnauthorized = &AppError{
        Code:    http.StatusUnauthorized,
        Message: "未授权访问",
        Type:    "auth_error",
    }
    
    ErrForbidden = &AppError{
        Code:    http.StatusForbidden,
        Message: "权限不足",
        Type:    "permission_error",
    }
    
    ErrNotFound = &AppError{
        Code:    http.StatusNotFound,
        Message: "资源不存在",
        Type:    "not_found_error",
    }
    
    ErrInternalServer = &AppError{
        Code:    http.StatusInternalServerError,
        Message: "服务器内部错误",
        Type:    "internal_error",
    }
)

// 错误处理中间件
func ErrorHandler() app.HandlerFunc {
    return func(ctx context.Context, c *app.RequestContext) {
        c.Next(ctx)
        
        if len(c.Errors) > 0 {
            err := c.Errors.Last()
            
            var appErr *AppError
            if e, ok := err.Err.(*AppError); ok {
                appErr = e
            } else {
                appErr = ErrInternalServer
                appErr.Details = err.Error()
            }
            
            c.JSON(appErr.Code, gin.H{
                "code":      appErr.Code,
                "message":   appErr.Message,
                "error":     appErr,
                "timestamp": time.Now().Format(time.RFC3339),
            })
        }
    }
}
```

## 4. 外部服务集成

### 4.1 Docker客户端
```go
package docker

import (
    "context"
    "github.com/docker/docker/client"
    "github.com/docker/docker/api/types"
)

type Client struct {
    cli *client.Client
}

func NewClient() (*Client, error) {
    cli, err := client.NewClientWithOpts(client.FromEnv)
    if err != nil {
        return nil, err
    }
    
    return &Client{cli: cli}, nil
}

func (c *Client) ListContainers(ctx context.Context) ([]types.Container, error) {
    return c.cli.ContainerList(ctx, types.ContainerListOptions{All: true})
}

func (c *Client) CreateContainer(ctx context.Context, config *container.Config, hostConfig *container.HostConfig) (string, error) {
    resp, err := c.cli.ContainerCreate(ctx, config, hostConfig, nil, nil, "")
    if err != nil {
        return "", err
    }
    return resp.ID, nil
}
```

### 4.2 SSH客户端
```go
package ssh

import (
    "golang.org/x/crypto/ssh"
    "net"
    "time"
)

type Client struct {
    client *ssh.Client
}

func NewClient(host, user, password string, port int) (*Client, error) {
    config := &ssh.ClientConfig{
        User: user,
        Auth: []ssh.AuthMethod{
            ssh.Password(password),
        },
        HostKeyCallback: ssh.InsecureIgnoreHostKey(),
        Timeout:         30 * time.Second,
    }
    
    addr := fmt.Sprintf("%s:%d", host, port)
    client, err := ssh.Dial("tcp", addr, config)
    if err != nil {
        return nil, err
    }
    
    return &Client{client: client}, nil
}

func (c *Client) ExecuteCommand(command string) (string, error) {
    session, err := c.client.NewSession()
    if err != nil {
        return "", err
    }
    defer session.Close()
    
    output, err := session.CombinedOutput(command)
    return string(output), err
}
```

## 5. 性能优化

### 5.1 数据库连接池
```go
func (d *Database) Configure() error {
    sqlDB, err := d.DB.DB()
    if err != nil {
        return err
    }
    
    // 设置连接池参数
    sqlDB.SetMaxIdleConns(10)
    sqlDB.SetMaxOpenConns(100)
    sqlDB.SetConnMaxLifetime(time.Hour)
    
    return nil
}
```

### 5.2 缓存机制
```go
package cache

import (
    "time"
    "github.com/patrickmn/go-cache"
)

type Cache struct {
    cache *cache.Cache
}

func New() *Cache {
    return &Cache{
        cache: cache.New(5*time.Minute, 10*time.Minute),
    }
}

func (c *Cache) Set(key string, value interface{}, duration time.Duration) {
    c.cache.Set(key, value, duration)
}

func (c *Cache) Get(key string) (interface{}, bool) {
    return c.cache.Get(key)
}
```

### 5.3 异步任务处理
```go
package worker

import (
    "context"
    "sync"
)

type Worker struct {
    taskChan chan Task
    wg       sync.WaitGroup
}

type Task func() error

func NewWorker(workerCount int) *Worker {
    w := &Worker{
        taskChan: make(chan Task, 100),
    }
    
    // 启动工作协程
    for i := 0; i < workerCount; i++ {
        go w.worker()
    }
    
    return w
}

func (w *Worker) Submit(task Task) {
    w.taskChan <- task
}

func (w *Worker) worker() {
    for task := range w.taskChan {
        if err := task(); err != nil {
            // 记录错误日志
            log.Error("任务执行失败:", err)
        }
    }
}
```
