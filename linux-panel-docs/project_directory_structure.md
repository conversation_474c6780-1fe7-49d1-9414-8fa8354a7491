# 项目目录结构设计

## 1. 整体项目结构

```
ops-panel/
├── backend/                      # 后端项目 (Go + <PERSON><PERSON>)
├── frontend/                     # 前端项目 (Vue3 + TDesign)
├── docker/                       # Docker相关文件
├── scripts/                      # 部署和运维脚本
├── docs/                         # 项目文档
├── configs/                      # 配置文件模板
├── docker-compose.yml            # Docker Compose配置
├── Makefile                      # 构建脚本
├── README.md                     # 项目说明
└── LICENSE                       # 开源协议
```

## 2. 后端项目结构 (Go + Hertz)

```
backend/
├── cmd/                          # 应用入口
│   └── server/
│       ├── main.go              # 主程序入口
│       ├── wire.go              # 依赖注入配置 (Wire)
│       └── wire_gen.go          # Wire生成的代码
├── internal/                     # 内部代码 (不对外暴露)
│   ├── app/                     # 应用层
│   │   ├── handler/             # HTTP处理器 (Hertz Controllers)
│   │   │   ├── auth/           # 认证相关处理器
│   │   │   │   ├── login.go
│   │   │   │   ├── logout.go
│   │   │   │   └── profile.go
│   │   │   ├── dashboard/      # 仪表板处理器
│   │   │   │   ├── overview.go
│   │   │   │   ├── stats.go
│   │   │   │   └── monitor.go
│   │   │   ├── container/      # 容器管理处理器
│   │   │   │   ├── container.go
│   │   │   │   ├── image.go
│   │   │   │   ├── network.go
│   │   │   │   └── volume.go
│   │   │   ├── database/       # 数据库管理处理器
│   │   │   │   ├── mysql.go
│   │   │   │   ├── postgresql.go
│   │   │   │   └── redis.go
│   │   │   ├── file/           # 文件管理处理器
│   │   │   │   ├── file.go
│   │   │   │   ├── upload.go
│   │   │   │   └── download.go
│   │   │   ├── monitor/        # 系统监控处理器
│   │   │   │   ├── system.go
│   │   │   │   ├── process.go
│   │   │   │   └── network.go
│   │   │   ├── website/        # 网站管理处理器
│   │   │   │   ├── website.go
│   │   │   │   ├── ssl.go
│   │   │   │   └── nginx.go
│   │   │   ├── setting/        # 系统设置处理器
│   │   │   │   ├── system.go
│   │   │   │   ├── security.go
│   │   │   │   └── backup.go
│   │   │   └── common.go       # 通用处理器
│   │   ├── service/            # 业务逻辑层
│   │   │   ├── auth/           # 认证服务
│   │   │   ├── container/      # 容器服务
│   │   │   ├── database/       # 数据库服务
│   │   │   ├── file/           # 文件服务
│   │   │   ├── monitor/        # 监控服务
│   │   │   ├── website/        # 网站服务
│   │   │   └── setting/        # 设置服务
│   │   ├── repository/         # 数据访问层
│   │   │   ├── user.go
│   │   │   ├── container.go
│   │   │   ├── database.go
│   │   │   ├── website.go
│   │   │   └── setting.go
│   │   └── dto/                # 数据传输对象
│   │       ├── request/        # 请求DTO
│   │       │   ├── auth.go
│   │       │   ├── container.go
│   │       │   ├── database.go
│   │       │   └── common.go
│   │       └── response/       # 响应DTO
│   │           ├── auth.go
│   │           ├── container.go
│   │           ├── database.go
│   │           └── common.go
│   ├── domain/                 # 领域层
│   │   ├── entity/            # 实体定义
│   │   │   ├── user.go
│   │   │   ├── container.go
│   │   │   ├── database.go
│   │   │   ├── website.go
│   │   │   ├── setting.go
│   │   │   └── base.go        # 基础实体
│   │   ├── repository/        # 仓储接口定义
│   │   │   ├── user.go
│   │   │   ├── container.go
│   │   │   └── database.go
│   │   └── service/           # 领域服务接口
│   │       ├── auth.go
│   │       ├── container.go
│   │       └── database.go
│   ├── infrastructure/         # 基础设施层
│   │   ├── database/          # 数据库相关
│   │   │   ├── sqlite.go      # SQLite连接配置
│   │   │   ├── migration.go   # 数据库迁移
│   │   │   └── transaction.go # 事务管理
│   │   ├── docker/            # Docker客户端
│   │   │   ├── client.go      # Docker客户端封装
│   │   │   ├── container.go   # 容器操作
│   │   │   ├── image.go       # 镜像操作
│   │   │   └── network.go     # 网络操作
│   │   ├── ssh/               # SSH客户端
│   │   │   ├── client.go      # SSH连接管理
│   │   │   └── command.go     # 命令执行
│   │   ├── nginx/             # Nginx管理
│   │   │   ├── config.go      # 配置文件管理
│   │   │   └── reload.go      # 配置重载
│   │   ├── system/            # 系统调用
│   │   │   ├── info.go        # 系统信息获取
│   │   │   ├── process.go     # 进程管理
│   │   │   └── network.go     # 网络信息
│   │   └── storage/           # 存储相关
│   │       ├── local.go       # 本地文件存储
│   │       └── cloud.go       # 云存储接口
│   └── pkg/                   # 公共包
│       ├── middleware/        # 中间件
│       │   ├── auth.go        # 认证中间件
│       │   ├── cors.go        # CORS中间件
│       │   ├── logger.go      # 日志中间件
│       │   ├── recovery.go    # 恢复中间件
│       │   ├── ratelimit.go   # 限流中间件
│       │   └── validator.go   # 验证中间件
│       ├── utils/            # 工具函数
│       │   ├── crypto.go     # 加密工具
│       │   ├── jwt.go        # JWT工具
│       │   ├── hash.go       # 哈希工具
│       │   ├── file.go       # 文件工具
│       │   ├── time.go       # 时间工具
│       │   └── validator.go  # 验证工具
│       ├── config/           # 配置管理
│       │   ├── config.go     # 配置结构定义
│       │   ├── loader.go     # 配置加载器
│       │   └── validator.go  # 配置验证
│       ├── logger/           # 日志管理
│       │   ├── logger.go     # 日志接口
│       │   ├── zap.go        # Zap日志实现
│       │   └── formatter.go  # 日志格式化
│       ├── errors/           # 错误处理
│       │   ├── errors.go     # 错误定义
│       │   ├── codes.go      # 错误码定义
│       │   └── handler.go    # 错误处理器
│       └── response/         # 响应处理
│           ├── response.go   # 统一响应格式
│           └── pagination.go # 分页响应
├── api/                       # API文档
│   ├── openapi.yaml          # OpenAPI规范
│   └── docs/                 # 生成的API文档
├── configs/                   # 配置文件
│   ├── config.yaml           # 主配置文件
│   ├── config.dev.yaml       # 开发环境配置
│   └── config.prod.yaml      # 生产环境配置
├── scripts/                   # 脚本文件
│   ├── build.sh              # 构建脚本
│   ├── migrate.sh            # 数据库迁移脚本
│   └── deploy.sh             # 部署脚本
├── docker/                    # Docker相关
│   ├── Dockerfile            # Docker镜像构建文件
│   └── docker-entrypoint.sh  # 容器启动脚本
├── test/                      # 测试文件
│   ├── unit/                 # 单元测试
│   ├── integration/          # 集成测试
│   └── e2e/                  # 端到端测试
├── go.mod                     # Go模块定义
├── go.sum                     # Go模块校验
├── Makefile                   # 构建脚本
└── README.md                  # 项目说明
```

## 3. 前端项目结构 (Vue3 + TDesign)

```
frontend/
├── public/                    # 静态资源
│   ├── favicon.ico           # 网站图标
│   ├── index.html            # HTML模板
│   └── manifest.json         # PWA配置
├── src/
│   ├── api/                  # API接口定义
│   │   ├── auth.ts           # 认证相关API
│   │   ├── dashboard.ts      # 仪表板API
│   │   ├── container.ts      # 容器管理API
│   │   ├── database.ts       # 数据库管理API
│   │   ├── file.ts           # 文件管理API
│   │   ├── monitor.ts        # 系统监控API
│   │   ├── website.ts        # 网站管理API
│   │   ├── setting.ts        # 系统设置API
│   │   └── index.ts          # API统一导出
│   ├── components/           # 公共组件
│   │   ├── common/          # 通用组件
│   │   │   ├── DataTable.vue # 数据表格组件
│   │   │   ├── FormDialog.vue # 表单对话框组件
│   │   │   ├── SearchForm.vue # 搜索表单组件
│   │   │   ├── StatusTag.vue  # 状态标签组件
│   │   │   └── PageHeader.vue # 页面头部组件
│   │   ├── charts/          # 图表组件
│   │   │   ├── LineChart.vue # 折线图组件
│   │   │   ├── BarChart.vue  # 柱状图组件
│   │   │   ├── PieChart.vue  # 饼图组件
│   │   │   └── GaugeChart.vue # 仪表盘组件
│   │   └── forms/           # 表单组件
│   │       ├── UserForm.vue  # 用户表单
│   │       ├── ContainerForm.vue # 容器表单
│   │       └── DatabaseForm.vue  # 数据库表单
│   ├── views/               # 页面视图
│   │   ├── auth/           # 认证页面
│   │   │   ├── Login.vue   # 登录页面
│   │   │   └── Profile.vue # 个人资料页面
│   │   ├── dashboard/      # 仪表板
│   │   │   ├── index.vue   # 仪表板首页
│   │   │   ├── Overview.vue # 系统概览
│   │   │   └── Monitor.vue  # 实时监控
│   │   ├── container/      # 容器管理
│   │   │   ├── index.vue   # 容器列表
│   │   │   ├── Container.vue # 容器管理
│   │   │   ├── Image.vue   # 镜像管理
│   │   │   ├── Network.vue # 网络管理
│   │   │   └── Volume.vue  # 存储卷管理
│   │   ├── database/       # 数据库管理
│   │   │   ├── index.vue   # 数据库首页
│   │   │   ├── MySQL.vue   # MySQL管理
│   │   │   ├── PostgreSQL.vue # PostgreSQL管理
│   │   │   └── Redis.vue   # Redis管理
│   │   ├── file/           # 文件管理
│   │   │   ├── index.vue   # 文件管理首页
│   │   │   ├── Browser.vue # 文件浏览器
│   │   │   ├── Editor.vue  # 文件编辑器
│   │   │   └── Upload.vue  # 文件上传
│   │   ├── monitor/        # 系统监控
│   │   │   ├── index.vue   # 监控首页
│   │   │   ├── System.vue  # 系统监控
│   │   │   ├── Process.vue # 进程监控
│   │   │   └── Network.vue # 网络监控
│   │   ├── website/        # 网站管理
│   │   │   ├── index.vue   # 网站列表
│   │   │   ├── Website.vue # 网站管理
│   │   │   ├── SSL.vue     # SSL证书管理
│   │   │   └── Nginx.vue   # Nginx配置
│   │   └── setting/        # 系统设置
│   │       ├── index.vue   # 设置首页
│   │       ├── System.vue  # 系统设置
│   │       ├── Security.vue # 安全设置
│   │       └── Backup.vue  # 备份设置
│   ├── layout/             # 布局组件
│   │   ├── index.vue       # 主布局
│   │   ├── Header.vue      # 头部组件
│   │   ├── Sidebar.vue     # 侧边栏组件
│   │   ├── Breadcrumb.vue  # 面包屑组件
│   │   └── Footer.vue      # 底部组件
│   ├── router/             # 路由配置
│   │   ├── index.ts        # 路由主文件
│   │   ├── routes.ts       # 路由定义
│   │   └── guards.ts       # 路由守卫
│   ├── store/              # 状态管理 (Pinia)
│   │   ├── index.ts        # Store主文件
│   │   ├── user.ts         # 用户状态
│   │   ├── system.ts       # 系统状态
│   │   ├── container.ts    # 容器状态
│   │   └── setting.ts      # 设置状态
│   ├── utils/              # 工具函数
│   │   ├── request.ts      # HTTP请求封装
│   │   ├── auth.ts         # 认证工具
│   │   ├── storage.ts      # 本地存储工具
│   │   ├── format.ts       # 格式化工具
│   │   ├── validation.ts   # 验证工具
│   │   └── websocket.ts    # WebSocket工具
│   ├── hooks/              # 组合式函数
│   │   ├── useAuth.ts      # 认证Hook
│   │   ├── useTable.ts     # 表格Hook
│   │   ├── useForm.ts      # 表单Hook
│   │   ├── useWebSocket.ts # WebSocket Hook
│   │   └── usePermission.ts # 权限Hook
│   ├── types/              # TypeScript类型定义
│   │   ├── api.ts          # API类型定义
│   │   ├── user.ts         # 用户类型
│   │   ├── container.ts    # 容器类型
│   │   ├── database.ts     # 数据库类型
│   │   └── common.ts       # 通用类型
│   ├── styles/             # 样式文件
│   │   ├── index.scss      # 主样式文件
│   │   ├── variables.scss  # 样式变量
│   │   ├── mixins.scss     # 样式混入
│   │   └── components.scss # 组件样式
│   ├── assets/             # 静态资源
│   │   ├── images/         # 图片资源
│   │   ├── icons/          # 图标资源
│   │   └── fonts/          # 字体资源
│   ├── plugins/            # 插件配置
│   │   ├── tdesign.ts      # TDesign配置
│   │   └── echarts.ts      # ECharts配置
│   ├── App.vue             # 根组件
│   └── main.ts             # 应用入口
├── tests/                  # 测试文件
│   ├── unit/               # 单元测试
│   ├── e2e/                # 端到端测试
│   └── setup.ts            # 测试配置
├── package.json            # 项目依赖
├── package-lock.json       # 依赖锁定文件
├── vite.config.ts          # Vite配置
├── tsconfig.json           # TypeScript配置
├── tailwind.config.js      # Tailwind CSS配置
├── .eslintrc.js            # ESLint配置
├── .prettierrc             # Prettier配置
└── README.md               # 项目说明
```

## 4. 目录结构设计原则

### 4.1 分层清晰
- **表现层**：Handler/Controller层处理HTTP请求
- **业务层**：Service层处理业务逻辑
- **数据层**：Repository层处理数据访问
- **领域层**：Entity/Domain层定义业务实体

### 4.2 职责单一
- 每个目录和文件都有明确的职责
- 避免循环依赖和紧耦合
- 便于单元测试和维护

### 4.3 可扩展性
- 模块化设计，便于功能扩展
- 插件化架构，支持第三方扩展
- 配置化管理，减少硬编码

### 4.4 开发友好
- 清晰的命名规范
- 完整的文档说明
- 便于新人理解和上手
