# 前端技术栈实现方案

## 1. 核心框架选择

### 1.1 前端框架：Vue 3 + Composition API
```typescript
// 选择理由：
// 1. Vue 3性能优异，支持Tree-shaking
// 2. Composition API提供更好的逻辑复用
// 3. TypeScript支持完善
// 4. 生态成熟，学习成本低

// main.ts
import { createApp } from 'vue'
import { createPinia } from 'pinia'
import TDesign from 'tdesign-vue-next'
import 'tdesign-vue-next/es/style/index.css'
import App from './App.vue'
import router from './router'

const app = createApp(App)

app.use(createPinia())
app.use(router)
app.use(TDesign)

app.mount('#app')
```

### 1.2 构建工具：Vite
```typescript
// vite.config.ts
import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'
import { resolve } from 'path'

export default defineConfig({
  plugins: [vue()],
  resolve: {
    alias: {
      '@': resolve(__dirname, 'src'),
      '@/components': resolve(__dirname, 'src/components'),
      '@/views': resolve(__dirname, 'src/views'),
      '@/api': resolve(__dirname, 'src/api'),
      '@/utils': resolve(__dirname, 'src/utils'),
    }
  },
  server: {
    port: 3000,
    proxy: {
      '/api': {
        target: 'http://localhost:8080',
        changeOrigin: true,
      }
    }
  },
  build: {
    outDir: 'dist',
    sourcemap: false,
    rollupOptions: {
      output: {
        chunkFileNames: 'js/[name]-[hash].js',
        entryFileNames: 'js/[name]-[hash].js',
        assetFileNames: '[ext]/[name]-[hash].[ext]'
      }
    }
  }
})
```

### 1.3 UI组件库：TDesign Vue
```typescript
// 选择理由：
// 1. 腾讯开源，企业级设计系统
// 2. 组件丰富，设计规范统一
// 3. TypeScript支持完善
// 4. 适合后台管理系统

// 按需引入配置
import { 
  Button, 
  Table, 
  Form, 
  Input, 
  Select, 
  DatePicker,
  Dialog,
  Message,
  Loading,
  Card,
  Layout,
  Menu,
  Breadcrumb
} from 'tdesign-vue-next'

const components = [
  Button, Table, Form, Input, Select, 
  DatePicker, Dialog, Card, Layout, 
  Menu, Breadcrumb
]

export default {
  install(app: App) {
    components.forEach(component => {
      app.use(component)
    })
    
    app.config.globalProperties.$message = Message
    app.config.globalProperties.$loading = Loading
  }
}
```

## 2. 状态管理：Pinia

### 2.1 用户状态管理
```typescript
// stores/user.ts
import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import type { User, LoginForm } from '@/types/user'
import { login, logout, getUserInfo } from '@/api/auth'

export const useUserStore = defineStore('user', () => {
  const user = ref<User | null>(null)
  const token = ref<string>(localStorage.getItem('token') || '')
  
  const isLoggedIn = computed(() => !!token.value)
  const userRole = computed(() => user.value?.role || 'guest')
  
  const loginAction = async (loginForm: LoginForm) => {
    try {
      const response = await login(loginForm)
      token.value = response.data.token
      user.value = response.data.user
      
      localStorage.setItem('token', token.value)
      return response
    } catch (error) {
      throw error
    }
  }
  
  const logoutAction = async () => {
    try {
      await logout()
    } finally {
      token.value = ''
      user.value = null
      localStorage.removeItem('token')
    }
  }
  
  const getUserInfoAction = async () => {
    if (!token.value) return
    
    try {
      const response = await getUserInfo()
      user.value = response.data
    } catch (error) {
      // Token可能已过期，清除本地状态
      logoutAction()
    }
  }
  
  return {
    user,
    token,
    isLoggedIn,
    userRole,
    loginAction,
    logoutAction,
    getUserInfoAction
  }
})
```

### 2.2 系统状态管理
```typescript
// stores/system.ts
import { defineStore } from 'pinia'
import { ref } from 'vue'
import type { SystemInfo, MonitorData } from '@/types/system'
import { getSystemInfo, getMonitorData } from '@/api/dashboard'

export const useSystemStore = defineStore('system', () => {
  const systemInfo = ref<SystemInfo | null>(null)
  const monitorData = ref<MonitorData | null>(null)
  const loading = ref(false)
  
  const fetchSystemInfo = async () => {
    loading.value = true
    try {
      const response = await getSystemInfo()
      systemInfo.value = response.data
    } finally {
      loading.value = false
    }
  }
  
  const fetchMonitorData = async () => {
    try {
      const response = await getMonitorData()
      monitorData.value = response.data
    } catch (error) {
      console.error('获取监控数据失败:', error)
    }
  }
  
  return {
    systemInfo,
    monitorData,
    loading,
    fetchSystemInfo,
    fetchMonitorData
  }
})
```

## 3. 路由配置

### 3.1 路由设计
```typescript
// router/index.ts
import { createRouter, createWebHistory } from 'vue-router'
import type { RouteRecordRaw } from 'vue-router'
import { useUserStore } from '@/stores/user'

const routes: RouteRecordRaw[] = [
  {
    path: '/login',
    name: 'Login',
    component: () => import('@/views/auth/Login.vue'),
    meta: { requiresAuth: false }
  },
  {
    path: '/',
    name: 'Layout',
    component: () => import('@/layout/index.vue'),
    meta: { requiresAuth: true },
    redirect: '/dashboard',
    children: [
      {
        path: 'dashboard',
        name: 'Dashboard',
        component: () => import('@/views/dashboard/index.vue'),
        meta: { title: '仪表板', icon: 'dashboard' }
      },
      {
        path: 'containers',
        name: 'Containers',
        component: () => import('@/views/container/index.vue'),
        meta: { title: '容器管理', icon: 'container' }
      },
      {
        path: 'databases',
        name: 'Databases',
        component: () => import('@/views/database/index.vue'),
        meta: { title: '数据库', icon: 'database' }
      },
      {
        path: 'files',
        name: 'Files',
        component: () => import('@/views/file/index.vue'),
        meta: { title: '文件管理', icon: 'folder' }
      },
      {
        path: 'websites',
        name: 'Websites',
        component: () => import('@/views/website/index.vue'),
        meta: { title: '网站管理', icon: 'internet' }
      },
      {
        path: 'monitor',
        name: 'Monitor',
        component: () => import('@/views/monitor/index.vue'),
        meta: { title: '系统监控', icon: 'chart' }
      },
      {
        path: 'settings',
        name: 'Settings',
        component: () => import('@/views/setting/index.vue'),
        meta: { title: '系统设置', icon: 'setting' }
      }
    ]
  }
]

const router = createRouter({
  history: createWebHistory(),
  routes
})

// 路由守卫
router.beforeEach((to, from, next) => {
  const userStore = useUserStore()
  
  if (to.meta.requiresAuth && !userStore.isLoggedIn) {
    next('/login')
  } else if (to.path === '/login' && userStore.isLoggedIn) {
    next('/')
  } else {
    next()
  }
})

export default router
```

## 4. API接口封装

### 4.1 HTTP客户端
```typescript
// utils/request.ts
import axios, { type AxiosInstance, type AxiosRequestConfig } from 'axios'
import { Message } from 'tdesign-vue-next'
import { useUserStore } from '@/stores/user'
import router from '@/router'

const request: AxiosInstance = axios.create({
  baseURL: '/api',
  timeout: 30000,
  headers: {
    'Content-Type': 'application/json'
  }
})

// 请求拦截器
request.interceptors.request.use(
  (config) => {
    const userStore = useUserStore()
    if (userStore.token) {
      config.headers.Authorization = `Bearer ${userStore.token}`
    }
    return config
  },
  (error) => {
    return Promise.reject(error)
  }
)

// 响应拦截器
request.interceptors.response.use(
  (response) => {
    const { code, message, data } = response.data
    
    if (code === 200) {
      return { data, message }
    } else {
      Message.error(message || '请求失败')
      return Promise.reject(new Error(message))
    }
  },
  (error) => {
    if (error.response) {
      const { status, data } = error.response
      
      switch (status) {
        case 401:
          Message.error('登录已过期，请重新登录')
          const userStore = useUserStore()
          userStore.logoutAction()
          router.push('/login')
          break
        case 403:
          Message.error('权限不足')
          break
        case 404:
          Message.error('请求的资源不存在')
          break
        case 500:
          Message.error('服务器内部错误')
          break
        default:
          Message.error(data?.message || '请求失败')
      }
    } else {
      Message.error('网络错误，请检查网络连接')
    }
    
    return Promise.reject(error)
  }
)

export default request
```

### 4.2 API接口定义
```typescript
// api/container.ts
import request from '@/utils/request'
import type { 
  Container, 
  ContainerCreateForm, 
  ContainerListParams,
  PaginationResponse 
} from '@/types/container'

export const getContainers = (params: ContainerListParams) => {
  return request.get<PaginationResponse<Container>>('/containers', { params })
}

export const getContainer = (id: string) => {
  return request.get<Container>(`/containers/${id}`)
}

export const createContainer = (data: ContainerCreateForm) => {
  return request.post<Container>('/containers', data)
}

export const updateContainer = (id: string, data: Partial<ContainerCreateForm>) => {
  return request.put<Container>(`/containers/${id}`, data)
}

export const deleteContainer = (id: string) => {
  return request.delete(`/containers/${id}`)
}

export const startContainer = (id: string) => {
  return request.post(`/containers/${id}/start`)
}

export const stopContainer = (id: string) => {
  return request.post(`/containers/${id}/stop`)
}

export const restartContainer = (id: string) => {
  return request.post(`/containers/${id}/restart`)
}

export const getContainerLogs = (id: string, params?: { lines?: number }) => {
  return request.get<string>(`/containers/${id}/logs`, { params })
}

export const getContainerStats = (id: string) => {
  return request.get(`/containers/${id}/stats`)
}
```

## 5. 组件设计

### 5.1 通用表格组件
```vue
<!-- components/common/DataTable.vue -->
<template>
  <div class="data-table">
    <div class="table-header" v-if="$slots.header">
      <slot name="header"></slot>
    </div>
    
    <t-table
      :data="data"
      :columns="columns"
      :loading="loading"
      :pagination="paginationConfig"
      :selected-row-keys="selectedRowKeys"
      @select-change="handleSelectChange"
      @page-change="handlePageChange"
      @sort-change="handleSortChange"
    >
      <template #operation="{ row }" v-if="showOperation">
        <slot name="operation" :row="row">
          <t-space>
            <t-button 
              theme="primary" 
              variant="text" 
              size="small"
              @click="$emit('edit', row)"
            >
              编辑
            </t-button>
            <t-button 
              theme="danger" 
              variant="text" 
              size="small"
              @click="$emit('delete', row)"
            >
              删除
            </t-button>
          </t-space>
        </slot>
      </template>
    </t-table>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import type { TableProps, PaginationProps } from 'tdesign-vue-next'

interface Props {
  data: any[]
  columns: TableProps['columns']
  loading?: boolean
  pagination?: PaginationProps
  selectedRowKeys?: (string | number)[]
  showOperation?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  loading: false,
  showOperation: true
})

const emit = defineEmits<{
  selectChange: [selectedRowKeys: (string | number)[]]
  pageChange: [pageInfo: { current: number; pageSize: number }]
  sortChange: [sortInfo: { sortBy: string; descending: boolean }]
  edit: [row: any]
  delete: [row: any]
}>()

const paginationConfig = computed(() => ({
  showJumper: true,
  showSizeChanger: true,
  pageSizeOptions: [10, 20, 50, 100],
  ...props.pagination
}))

const handleSelectChange = (selectedRowKeys: (string | number)[]) => {
  emit('selectChange', selectedRowKeys)
}

const handlePageChange = (pageInfo: { current: number; pageSize: number }) => {
  emit('pageChange', pageInfo)
}

const handleSortChange = (sortInfo: { sortBy: string; descending: boolean }) => {
  emit('sortChange', sortInfo)
}
</script>
```

### 5.2 表单组件
```vue
<!-- components/common/FormDialog.vue -->
<template>
  <t-dialog
    v-model:visible="visible"
    :header="title"
    :width="width"
    :confirm-btn="confirmBtn"
    :cancel-btn="cancelBtn"
    @confirm="handleConfirm"
    @cancel="handleCancel"
  >
    <t-form
      ref="formRef"
      :model="formData"
      :rules="rules"
      :label-width="labelWidth"
      @submit="handleSubmit"
    >
      <slot :form-data="formData"></slot>
    </t-form>
  </t-dialog>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue'
import type { FormInstanceFunctions, FormRule } from 'tdesign-vue-next'

interface Props {
  visible: boolean
  title: string
  width?: string | number
  labelWidth?: string | number
  formData: Record<string, any>
  rules?: Record<string, FormRule[]>
  confirmBtn?: string | { content: string; loading?: boolean }
  cancelBtn?: string | boolean
}

const props = withDefaults(defineProps<Props>(), {
  width: 600,
  labelWidth: 100,
  confirmBtn: '确定',
  cancelBtn: '取消'
})

const emit = defineEmits<{
  'update:visible': [visible: boolean]
  'update:formData': [formData: Record<string, any>]
  confirm: [formData: Record<string, any>]
  cancel: []
}>()

const formRef = ref<FormInstanceFunctions>()

const handleConfirm = async () => {
  const result = await formRef.value?.validate()
  if (result === true) {
    emit('confirm', props.formData)
  }
}

const handleCancel = () => {
  emit('update:visible', false)
  emit('cancel')
}

const handleSubmit = (e: SubmitEvent) => {
  e.preventDefault()
  handleConfirm()
}

watch(
  () => props.visible,
  (visible) => {
    if (!visible) {
      formRef.value?.reset()
    }
  }
)
</script>
```

## 6. 工具函数

### 6.1 通用工具
```typescript
// utils/common.ts
export const formatBytes = (bytes: number, decimals = 2): string => {
  if (bytes === 0) return '0 Bytes'
  
  const k = 1024
  const dm = decimals < 0 ? 0 : decimals
  const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB']
  
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(dm)) + ' ' + sizes[i]
}

export const formatDuration = (seconds: number): string => {
  const hours = Math.floor(seconds / 3600)
  const minutes = Math.floor((seconds % 3600) / 60)
  const secs = seconds % 60
  
  if (hours > 0) {
    return `${hours}h ${minutes}m ${secs}s`
  } else if (minutes > 0) {
    return `${minutes}m ${secs}s`
  } else {
    return `${secs}s`
  }
}

export const debounce = <T extends (...args: any[]) => any>(
  func: T,
  wait: number
): ((...args: Parameters<T>) => void) => {
  let timeout: NodeJS.Timeout
  
  return (...args: Parameters<T>) => {
    clearTimeout(timeout)
    timeout = setTimeout(() => func.apply(this, args), wait)
  }
}

export const throttle = <T extends (...args: any[]) => any>(
  func: T,
  limit: number
): ((...args: Parameters<T>) => void) => {
  let inThrottle: boolean
  
  return (...args: Parameters<T>) => {
    if (!inThrottle) {
      func.apply(this, args)
      inThrottle = true
      setTimeout(() => inThrottle = false, limit)
    }
  }
}
```

### 6.2 WebSocket工具
```typescript
// utils/websocket.ts
import { ref, onUnmounted } from 'vue'

export const useWebSocket = (url: string) => {
  const socket = ref<WebSocket | null>(null)
  const isConnected = ref(false)
  const data = ref<any>(null)
  const error = ref<Event | null>(null)
  
  const connect = () => {
    socket.value = new WebSocket(url)
    
    socket.value.onopen = () => {
      isConnected.value = true
      error.value = null
    }
    
    socket.value.onmessage = (event) => {
      try {
        data.value = JSON.parse(event.data)
      } catch {
        data.value = event.data
      }
    }
    
    socket.value.onerror = (event) => {
      error.value = event
    }
    
    socket.value.onclose = () => {
      isConnected.value = false
    }
  }
  
  const disconnect = () => {
    if (socket.value) {
      socket.value.close()
      socket.value = null
      isConnected.value = false
    }
  }
  
  const send = (message: any) => {
    if (socket.value && isConnected.value) {
      const data = typeof message === 'string' ? message : JSON.stringify(message)
      socket.value.send(data)
    }
  }
  
  onUnmounted(() => {
    disconnect()
  })
  
  return {
    socket,
    isConnected,
    data,
    error,
    connect,
    disconnect,
    send
  }
}
```
