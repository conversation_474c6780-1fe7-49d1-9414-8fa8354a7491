# 部署方案设计

## 1. 容器化部署

### 1.1 后端Dockerfile
```dockerfile
# 多阶段构建
FROM golang:1.21-alpine AS builder

WORKDIR /app

# 安装依赖
RUN apk add --no-cache git ca-certificates tzdata

# 复制go mod文件
COPY go.mod go.sum ./
RUN go mod download

# 复制源代码
COPY . .

# 构建应用
RUN CGO_ENABLED=0 GOOS=linux go build -a -installsuffix cgo -o main ./cmd/server

# 运行阶段
FROM alpine:latest

RUN apk --no-cache add ca-certificates tzdata

WORKDIR /root/

# 从构建阶段复制二进制文件
COPY --from=builder /app/main .
COPY --from=builder /app/configs ./configs

# 创建数据目录
RUN mkdir -p /data/logs /data/database /data/uploads

# 设置时区
ENV TZ=Asia/Shanghai

# 暴露端口
EXPOSE 8080

# 启动命令
CMD ["./main"]
```

### 1.2 前端Dockerfile
```dockerfile
# 构建阶段
FROM node:18-alpine AS builder

WORKDIR /app

# 复制package文件
COPY package*.json ./
RUN npm ci --only=production

# 复制源代码
COPY . .

# 构建应用
RUN npm run build

# 运行阶段
FROM nginx:alpine

# 复制构建产物
COPY --from=builder /app/dist /usr/share/nginx/html

# 复制nginx配置
COPY nginx.conf /etc/nginx/nginx.conf

# 暴露端口
EXPOSE 80

# 启动nginx
CMD ["nginx", "-g", "daemon off;"]
```

### 1.3 Docker Compose配置
```yaml
# docker-compose.yml
version: '3.8'

services:
  # 后端服务
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    container_name: ops-panel-backend
    restart: unless-stopped
    ports:
      - "8080:8080"
    volumes:
      - ./data:/data
      - /var/run/docker.sock:/var/run/docker.sock
      - /etc/localtime:/etc/localtime:ro
    environment:
      - GIN_MODE=release
      - TZ=Asia/Shanghai
    networks:
      - ops-panel-network
    depends_on:
      - redis
    healthcheck:
      test: ["CMD", "wget", "--quiet", "--tries=1", "--spider", "http://localhost:8080/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # 前端服务
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
    container_name: ops-panel-frontend
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./ssl:/etc/nginx/ssl:ro
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf:ro
    networks:
      - ops-panel-network
    depends_on:
      - backend

  # Redis缓存
  redis:
    image: redis:7-alpine
    container_name: ops-panel-redis
    restart: unless-stopped
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
      - ./redis/redis.conf:/etc/redis/redis.conf:ro
    command: redis-server /etc/redis/redis.conf
    networks:
      - ops-panel-network

  # Nginx反向代理
  nginx:
    image: nginx:alpine
    container_name: ops-panel-nginx
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./ssl:/etc/nginx/ssl:ro
      - ./logs/nginx:/var/log/nginx
    networks:
      - ops-panel-network
    depends_on:
      - frontend
      - backend

volumes:
  redis_data:

networks:
  ops-panel-network:
    driver: bridge
```

## 2. 一键安装脚本

### 2.1 安装脚本
```bash
#!/bin/bash
# install.sh - Linux运维面板一键安装脚本

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查系统要求
check_system() {
    log_info "检查系统环境..."
    
    # 检查操作系统
    if [[ ! -f /etc/os-release ]]; then
        log_error "不支持的操作系统"
        exit 1
    fi
    
    # 检查架构
    ARCH=$(uname -m)
    if [[ "$ARCH" != "x86_64" && "$ARCH" != "aarch64" ]]; then
        log_error "不支持的系统架构: $ARCH"
        exit 1
    fi
    
    # 检查内存
    MEMORY=$(free -m | awk 'NR==2{printf "%.0f", $2}')
    if [[ $MEMORY -lt 1024 ]]; then
        log_warn "系统内存不足1GB，可能影响运行性能"
    fi
    
    # 检查磁盘空间
    DISK=$(df -h / | awk 'NR==2{print $4}' | sed 's/G//')
    if [[ ${DISK%.*} -lt 10 ]]; then
        log_warn "系统磁盘空间不足10GB，可能影响正常使用"
    fi
    
    log_info "系统环境检查完成"
}

# 安装Docker
install_docker() {
    if command -v docker &> /dev/null; then
        log_info "Docker已安装，跳过安装步骤"
        return
    fi
    
    log_info "安装Docker..."
    
    # 更新包索引
    apt-get update
    
    # 安装必要的包
    apt-get install -y \
        apt-transport-https \
        ca-certificates \
        curl \
        gnupg \
        lsb-release
    
    # 添加Docker官方GPG密钥
    curl -fsSL https://download.docker.com/linux/ubuntu/gpg | gpg --dearmor -o /usr/share/keyrings/docker-archive-keyring.gpg
    
    # 添加Docker仓库
    echo \
        "deb [arch=$(dpkg --print-architecture) signed-by=/usr/share/keyrings/docker-archive-keyring.gpg] https://download.docker.com/linux/ubuntu \
        $(lsb_release -cs) stable" | tee /etc/apt/sources.list.d/docker.list > /dev/null
    
    # 安装Docker Engine
    apt-get update
    apt-get install -y docker-ce docker-ce-cli containerd.io
    
    # 启动Docker服务
    systemctl start docker
    systemctl enable docker
    
    log_info "Docker安装完成"
}

# 安装Docker Compose
install_docker_compose() {
    if command -v docker-compose &> /dev/null; then
        log_info "Docker Compose已安装，跳过安装步骤"
        return
    fi
    
    log_info "安装Docker Compose..."
    
    # 下载Docker Compose
    COMPOSE_VERSION=$(curl -s https://api.github.com/repos/docker/compose/releases/latest | grep 'tag_name' | cut -d\" -f4)
    curl -L "https://github.com/docker/compose/releases/download/${COMPOSE_VERSION}/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
    
    # 设置执行权限
    chmod +x /usr/local/bin/docker-compose
    
    log_info "Docker Compose安装完成"
}

# 创建目录结构
create_directories() {
    log_info "创建目录结构..."
    
    mkdir -p /opt/ops-panel/{data,logs,ssl,configs}
    mkdir -p /opt/ops-panel/data/{database,uploads,backups}
    mkdir -p /opt/ops-panel/logs/{app,nginx}
    
    log_info "目录结构创建完成"
}

# 下载应用文件
download_app() {
    log_info "下载应用文件..."
    
    cd /opt/ops-panel
    
    # 下载最新版本
    LATEST_VERSION=$(curl -s https://api.github.com/repos/your-org/ops-panel/releases/latest | grep 'tag_name' | cut -d\" -f4)
    curl -L "https://github.com/your-org/ops-panel/releases/download/${LATEST_VERSION}/ops-panel-${LATEST_VERSION}.tar.gz" -o ops-panel.tar.gz
    
    # 解压文件
    tar -xzf ops-panel.tar.gz
    rm ops-panel.tar.gz
    
    log_info "应用文件下载完成"
}

# 生成配置文件
generate_config() {
    log_info "生成配置文件..."
    
    # 生成随机密码
    DB_PASSWORD=$(openssl rand -base64 32)
    JWT_SECRET=$(openssl rand -base64 64)
    
    # 生成应用配置
    cat > /opt/ops-panel/configs/config.yaml << EOF
server:
  host: 0.0.0.0
  port: 8080
  read_timeout: 30
  write_timeout: 30

database:
  driver: sqlite
  dsn: /data/database/ops_panel.db
  max_idle: 10
  max_open: 100
  log_level: info

security:
  jwt_secret: ${JWT_SECRET}
  session_timeout: 3600
  password_salt: $(openssl rand -base64 32)

docker:
  socket: /var/run/docker.sock
  registry: docker.io

logging:
  level: info
  file: /data/logs/app.log
  max_size: 100
  max_backups: 3
  max_age: 28
EOF
    
    log_info "配置文件生成完成"
}

# 启动服务
start_services() {
    log_info "启动服务..."
    
    cd /opt/ops-panel
    docker-compose up -d
    
    # 等待服务启动
    sleep 10
    
    # 检查服务状态
    if docker-compose ps | grep -q "Up"; then
        log_info "服务启动成功"
    else
        log_error "服务启动失败"
        docker-compose logs
        exit 1
    fi
}

# 创建系统服务
create_systemd_service() {
    log_info "创建系统服务..."
    
    cat > /etc/systemd/system/ops-panel.service << EOF
[Unit]
Description=Linux Ops Panel
Requires=docker.service
After=docker.service

[Service]
Type=oneshot
RemainAfterExit=yes
WorkingDirectory=/opt/ops-panel
ExecStart=/usr/local/bin/docker-compose up -d
ExecStop=/usr/local/bin/docker-compose down
TimeoutStartSec=0

[Install]
WantedBy=multi-user.target
EOF
    
    systemctl daemon-reload
    systemctl enable ops-panel
    
    log_info "系统服务创建完成"
}

# 配置防火墙
configure_firewall() {
    log_info "配置防火墙..."
    
    if command -v ufw &> /dev/null; then
        ufw allow 80/tcp
        ufw allow 443/tcp
        ufw allow 8080/tcp
        log_info "UFW防火墙规则已添加"
    elif command -v firewall-cmd &> /dev/null; then
        firewall-cmd --permanent --add-port=80/tcp
        firewall-cmd --permanent --add-port=443/tcp
        firewall-cmd --permanent --add-port=8080/tcp
        firewall-cmd --reload
        log_info "Firewalld防火墙规则已添加"
    else
        log_warn "未检测到防火墙，请手动开放80、443、8080端口"
    fi
}

# 显示安装信息
show_install_info() {
    log_info "安装完成！"
    echo
    echo "=========================================="
    echo "Linux运维面板安装成功"
    echo "=========================================="
    echo "访问地址: http://$(curl -s ifconfig.me):8080"
    echo "默认用户: admin"
    echo "默认密码: admin123"
    echo "配置目录: /opt/ops-panel/configs"
    echo "数据目录: /opt/ops-panel/data"
    echo "日志目录: /opt/ops-panel/logs"
    echo "=========================================="
    echo "管理命令:"
    echo "启动服务: systemctl start ops-panel"
    echo "停止服务: systemctl stop ops-panel"
    echo "重启服务: systemctl restart ops-panel"
    echo "查看状态: systemctl status ops-panel"
    echo "查看日志: docker-compose -f /opt/ops-panel/docker-compose.yml logs"
    echo "=========================================="
    echo
    log_warn "请及时修改默认密码！"
}

# 主函数
main() {
    log_info "开始安装Linux运维面板..."
    
    # 检查是否为root用户
    if [[ $EUID -ne 0 ]]; then
        log_error "请使用root用户运行此脚本"
        exit 1
    fi
    
    check_system
    install_docker
    install_docker_compose
    create_directories
    download_app
    generate_config
    start_services
    create_systemd_service
    configure_firewall
    show_install_info
    
    log_info "安装完成！"
}

# 执行主函数
main "$@"
```

## 3. 生产环境配置

### 3.1 Nginx配置
```nginx
# nginx.conf
user nginx;
worker_processes auto;
error_log /var/log/nginx/error.log warn;
pid /var/run/nginx.pid;

events {
    worker_connections 1024;
    use epoll;
    multi_accept on;
}

http {
    include /etc/nginx/mime.types;
    default_type application/octet-stream;
    
    # 日志格式
    log_format main '$remote_addr - $remote_user [$time_local] "$request" '
                    '$status $body_bytes_sent "$http_referer" '
                    '"$http_user_agent" "$http_x_forwarded_for"';
    
    access_log /var/log/nginx/access.log main;
    
    # 基础配置
    sendfile on;
    tcp_nopush on;
    tcp_nodelay on;
    keepalive_timeout 65;
    types_hash_max_size 2048;
    client_max_body_size 100M;
    
    # Gzip压缩
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_types text/plain text/css text/xml text/javascript 
               application/javascript application/xml+rss 
               application/json application/xml;
    
    # 安全头
    add_header X-Frame-Options DENY;
    add_header X-Content-Type-Options nosniff;
    add_header X-XSS-Protection "1; mode=block";
    
    # 上游服务器
    upstream backend {
        server backend:8080;
        keepalive 32;
    }
    
    # HTTP重定向到HTTPS
    server {
        listen 80;
        server_name _;
        return 301 https://$server_name$request_uri;
    }
    
    # HTTPS服务器
    server {
        listen 443 ssl http2;
        server_name _;
        
        # SSL配置
        ssl_certificate /etc/nginx/ssl/cert.pem;
        ssl_certificate_key /etc/nginx/ssl/key.pem;
        ssl_protocols TLSv1.2 TLSv1.3;
        ssl_ciphers ECDHE-RSA-AES128-GCM-SHA256:ECDHE-RSA-AES256-GCM-SHA384;
        ssl_prefer_server_ciphers off;
        ssl_session_cache shared:SSL:10m;
        ssl_session_timeout 10m;
        
        # 静态文件
        location / {
            root /usr/share/nginx/html;
            index index.html;
            try_files $uri $uri/ /index.html;
            
            # 缓存配置
            location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg)$ {
                expires 1y;
                add_header Cache-Control "public, immutable";
            }
        }
        
        # API代理
        location /api/ {
            proxy_pass http://backend;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            proxy_connect_timeout 30s;
            proxy_send_timeout 30s;
            proxy_read_timeout 30s;
        }
        
        # WebSocket代理
        location /api/ws/ {
            proxy_pass http://backend;
            proxy_http_version 1.1;
            proxy_set_header Upgrade $http_upgrade;
            proxy_set_header Connection "upgrade";
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            proxy_read_timeout 86400;
        }
    }
}
```

### 3.2 系统服务配置
```ini
# /etc/systemd/system/ops-panel.service
[Unit]
Description=Linux Ops Panel
Documentation=https://github.com/your-org/ops-panel
Requires=docker.service
After=docker.service
Wants=network-online.target
After=network-online.target

[Service]
Type=oneshot
RemainAfterExit=yes
User=root
Group=root
WorkingDirectory=/opt/ops-panel
Environment=COMPOSE_PROJECT_NAME=ops-panel
ExecStartPre=/usr/local/bin/docker-compose pull
ExecStart=/usr/local/bin/docker-compose up -d
ExecStop=/usr/local/bin/docker-compose down
ExecReload=/usr/local/bin/docker-compose restart
TimeoutStartSec=300
TimeoutStopSec=120
Restart=on-failure
RestartSec=10

[Install]
WantedBy=multi-user.target
```

## 4. 监控和日志

### 4.1 健康检查
```bash
#!/bin/bash
# health-check.sh

check_service() {
    local service=$1
    local url=$2
    
    if curl -f -s "$url" > /dev/null; then
        echo "✓ $service is healthy"
        return 0
    else
        echo "✗ $service is unhealthy"
        return 1
    fi
}

# 检查各个服务
check_service "Frontend" "http://localhost:80/health"
check_service "Backend" "http://localhost:8080/health"
check_service "Redis" "redis://localhost:6379"

# 检查Docker容器状态
docker-compose ps
```

### 4.2 日志轮转配置
```
# /etc/logrotate.d/ops-panel
/opt/ops-panel/logs/*.log {
    daily
    missingok
    rotate 30
    compress
    delaycompress
    notifempty
    create 644 root root
    postrotate
        docker-compose -f /opt/ops-panel/docker-compose.yml restart nginx
    endscript
}
```
