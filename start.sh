#!/bin/bash

# Linux运维管理面板快速启动脚本

echo "🚀 启动Linux运维管理面板..."

# 检查是否在正确的目录
if [ ! -f "docker-compose.yml" ]; then
    echo "❌ 请在项目根目录运行此脚本"
    exit 1
fi

# 创建必要的目录
mkdir -p data

echo "📋 选择启动方式："
echo "1. 开发模式 (推荐用于开发和测试)"
echo "2. Docker模式 (推荐用于生产环境)"
echo "3. 仅启动后端服务"
echo "4. 仅启动前端服务"

read -p "请选择 (1-4): " choice

case $choice in
    1)
        echo "🔧 启动开发模式..."
        make dev
        ;;
    2)
        echo "🐳 启动Docker模式..."
        make deploy
        ;;
    3)
        echo "🔨 启动后端服务..."
        cd backend && go run cmd/server/main.go
        ;;
    4)
        echo "🎨 启动前端服务..."
        cd frontend && npm run dev
        ;;
    *)
        echo "❌ 无效选择"
        exit 1
        ;;
esac
