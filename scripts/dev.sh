#!/bin/bash

# Linux运维管理面板开发环境启动脚本

set -e

echo "🚀 启动开发环境..."

# 检查Go是否安装
if ! command -v go &> /dev/null; then
    echo "❌ Go未安装，请先安装Go 1.21+"
    exit 1
fi

# 检查Node.js是否安装
if ! command -v node &> /dev/null; then
    echo "❌ Node.js未安装，请先安装Node.js 18+"
    exit 1
fi

# 创建必要的目录
mkdir -p data

# 启动后端服务
echo "🔨 启动后端服务..."
cd backend

# 下载Go依赖
if [ ! -d "vendor" ]; then
    echo "📦 下载Go依赖..."
    go mod tidy
fi

# 后台启动后端服务
echo "🚀 启动后端服务 (端口: 8080)..."
nohup go run cmd/server/main.go > ../data/backend.log 2>&1 &
BACKEND_PID=$!
echo $BACKEND_PID > ../data/backend.pid

cd ..

# 启动前端服务
echo "🔨 启动前端服务..."
cd frontend

# 安装前端依赖
if [ ! -d "node_modules" ]; then
    echo "📦 安装前端依赖..."
    npm install
fi

# 后台启动前端服务
echo "🚀 启动前端服务 (端口: 3000)..."
nohup npm run dev > ../data/frontend.log 2>&1 &
FRONTEND_PID=$!
echo $FRONTEND_PID > ../data/frontend.pid

cd ..

# 等待服务启动
echo "⏳ 等待服务启动..."
sleep 5

# 检查服务状态
echo "🔍 检查服务状态..."

# 检查后端
if curl -f http://localhost:8080/api/health &> /dev/null; then
    echo "✅ 后端服务启动成功 (http://localhost:8080)"
else
    echo "❌ 后端服务启动失败"
    echo "📋 后端日志："
    tail -n 20 data/backend.log
fi

# 检查前端
if curl -f http://localhost:3000 &> /dev/null; then
    echo "✅ 前端服务启动成功 (http://localhost:3000)"
else
    echo "⏳ 前端服务正在启动中..."
fi

echo ""
echo "🎉 开发环境启动完成！"
echo ""
echo "📝 服务访问地址："
echo "   前端: http://localhost:3000"
echo "   后端API: http://localhost:8080/api"
echo "   健康检查: http://localhost:8080/api/health"
echo ""
echo "👤 默认登录账户："
echo "   用户名: admin"
echo "   密码: admin123"
echo ""
echo "📋 开发命令："
echo "   查看后端日志: tail -f data/backend.log"
echo "   查看前端日志: tail -f data/frontend.log"
echo "   停止服务: ./scripts/stop-dev.sh"
echo ""
echo "💡 提示："
echo "   - 后端代码修改后需要重启服务"
echo "   - 前端代码修改会自动热重载"
echo "   - 数据库文件位于: data/panel.db"
