#!/bin/bash

# 停止开发环境脚本

echo "🛑 停止开发环境..."

# 停止后端服务
if [ -f "data/backend.pid" ]; then
    BACKEND_PID=$(cat data/backend.pid)
    if ps -p $BACKEND_PID > /dev/null; then
        echo "🛑 停止后端服务 (PID: $BACKEND_PID)..."
        kill $BACKEND_PID
        rm data/backend.pid
    else
        echo "⚠️  后端服务已停止"
        rm -f data/backend.pid
    fi
else
    echo "⚠️  未找到后端服务PID文件"
fi

# 停止前端服务
if [ -f "data/frontend.pid" ]; then
    FRONTEND_PID=$(cat data/frontend.pid)
    if ps -p $FRONTEND_PID > /dev/null; then
        echo "🛑 停止前端服务 (PID: $FRONTEND_PID)..."
        kill $FRONTEND_PID
        rm data/frontend.pid
    else
        echo "⚠️  前端服务已停止"
        rm -f data/frontend.pid
    fi
else
    echo "⚠️  未找到前端服务PID文件"
fi

# 清理可能残留的进程
echo "🧹 清理残留进程..."
pkill -f "go run cmd/server/main.go" 2>/dev/null || true
pkill -f "npm run dev" 2>/dev/null || true

echo "✅ 开发环境已停止"
