#!/bin/bash

# Linux运维管理面板部署脚本

set -e

echo "🚀 开始部署Linux运维管理面板..."

# 检查Docker是否运行
if ! docker info &> /dev/null; then
    echo "❌ Docker未运行，请启动Docker服务"
    exit 1
fi

# 停止现有服务
echo "🛑 停止现有服务..."
docker-compose down

# 清理旧镜像（可选）
read -p "是否清理旧镜像？(y/N): " -n 1 -r
echo
if [[ $REPLY =~ ^[Yy]$ ]]; then
    echo "🧹 清理旧镜像..."
    docker system prune -f
fi

# 启动服务
echo "🚀 启动服务..."
docker-compose up -d

# 等待服务启动
echo "⏳ 等待服务启动..."
sleep 10

# 检查服务状态
echo "🔍 检查服务状态..."
docker-compose ps

# 检查健康状态
echo "🏥 检查服务健康状态..."
for i in {1..30}; do
    if curl -f http://localhost/health &> /dev/null; then
        echo "✅ 服务启动成功！"
        break
    fi
    if [ $i -eq 30 ]; then
        echo "❌ 服务启动超时"
        echo "📋 查看日志："
        docker-compose logs
        exit 1
    fi
    echo "⏳ 等待服务启动... ($i/30)"
    sleep 2
done

echo ""
echo "🎉 部署完成！"
echo ""
echo "📝 服务访问地址："
echo "   前端: http://localhost"
echo "   后端API: http://localhost/api"
echo "   健康检查: http://localhost/health"
echo ""
echo "👤 默认登录账户："
echo "   用户名: admin"
echo "   密码: admin123"
echo ""
echo "📋 常用命令："
echo "   查看日志: docker-compose logs -f"
echo "   停止服务: docker-compose down"
echo "   重启服务: docker-compose restart"
echo "   查看状态: docker-compose ps"
