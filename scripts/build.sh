#!/bin/bash

# Linux运维管理面板构建脚本

set -e

echo "🚀 开始构建Linux运维管理面板..."

# 检查Docker是否安装
if ! command -v docker &> /dev/null; then
    echo "❌ Docker未安装，请先安装Docker"
    exit 1
fi

if ! command -v docker-compose &> /dev/null; then
    echo "❌ Docker Compose未安装，请先安装Docker Compose"
    exit 1
fi

# 创建必要的目录
echo "📁 创建必要的目录..."
mkdir -p data
mkdir -p ssl

# 构建后端
echo "🔨 构建后端服务..."
cd backend
if [ ! -f "go.mod" ]; then
    echo "❌ 后端go.mod文件不存在"
    exit 1
fi

# 下载Go依赖
echo "📦 下载Go依赖..."
go mod tidy

cd ..

# 构建前端
echo "🔨 构建前端服务..."
cd frontend
if [ ! -f "package.json" ]; then
    echo "❌ 前端package.json文件不存在"
    exit 1
fi

# 检查Node.js是否安装
if ! command -v node &> /dev/null; then
    echo "❌ Node.js未安装，请先安装Node.js"
    exit 1
fi

# 安装前端依赖
echo "📦 安装前端依赖..."
npm install

cd ..

# 使用Docker Compose构建
echo "🐳 使用Docker Compose构建镜像..."
docker-compose build

echo "✅ 构建完成！"
echo ""
echo "🎉 可以使用以下命令启动服务："
echo "   docker-compose up -d"
echo ""
echo "📝 服务访问地址："
echo "   前端: http://localhost"
echo "   后端API: http://localhost/api"
echo "   健康检查: http://localhost/health"
echo ""
echo "👤 默认登录账户："
echo "   用户名: admin"
echo "   密码: admin123"
