# Linux运维管理面板 Makefile

.PHONY: help build dev deploy stop clean test lint

# 默认目标
help:
	@echo "Linux运维管理面板 - 可用命令："
	@echo ""
	@echo "开发相关："
	@echo "  dev          启动开发环境"
	@echo "  stop-dev     停止开发环境"
	@echo "  test         运行测试"
	@echo "  lint         代码检查"
	@echo ""
	@echo "构建部署："
	@echo "  build        构建Docker镜像"
	@echo "  deploy       部署到生产环境"
	@echo "  stop         停止生产环境"
	@echo ""
	@echo "维护相关："
	@echo "  clean        清理构建文件"
	@echo "  logs         查看服务日志"
	@echo "  status       查看服务状态"

# 开发环境
dev:
	@chmod +x scripts/dev.sh
	@./scripts/dev.sh

stop-dev:
	@chmod +x scripts/stop-dev.sh
	@./scripts/stop-dev.sh

# 构建
build:
	@chmod +x scripts/build.sh
	@./scripts/build.sh

# 部署
deploy:
	@chmod +x scripts/deploy.sh
	@./scripts/deploy.sh

# 停止服务
stop:
	@docker-compose down

# 查看日志
logs:
	@docker-compose logs -f

# 查看状态
status:
	@docker-compose ps

# 测试
test:
	@echo "🧪 运行后端测试..."
	@cd backend && go test ./...
	@echo "🧪 运行前端测试..."
	@cd frontend && npm run test 2>/dev/null || echo "前端测试配置待完善"

# 代码检查
lint:
	@echo "🔍 后端代码检查..."
	@cd backend && go vet ./...
	@cd backend && go fmt ./...
	@echo "🔍 前端代码检查..."
	@cd frontend && npm run lint 2>/dev/null || echo "前端lint配置待完善"

# 清理
clean:
	@echo "🧹 清理构建文件..."
	@rm -rf backend/main
	@rm -rf frontend/dist
	@rm -rf frontend/node_modules/.cache
	@docker system prune -f
	@echo "✅ 清理完成"

# 重置数据库
reset-db:
	@echo "🗄️  重置数据库..."
	@rm -f data/panel.db
	@echo "✅ 数据库已重置"

# 备份数据
backup:
	@echo "💾 备份数据..."
	@mkdir -p backups
	@cp data/panel.db backups/panel-$(shell date +%Y%m%d-%H%M%S).db
	@echo "✅ 数据备份完成"

# 安装依赖
install:
	@echo "📦 安装后端依赖..."
	@cd backend && go mod tidy
	@echo "📦 安装前端依赖..."
	@cd frontend && npm install
	@echo "✅ 依赖安装完成"

# 更新依赖
update:
	@echo "🔄 更新后端依赖..."
	@cd backend && go get -u ./...
	@cd backend && go mod tidy
	@echo "🔄 更新前端依赖..."
	@cd frontend && npm update
	@echo "✅ 依赖更新完成"
