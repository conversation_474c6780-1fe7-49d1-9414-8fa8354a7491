{"name": "panel-frontend", "version": "1.0.0", "description": "Linux运维管理面板前端", "type": "module", "scripts": {"dev": "vite", "build": "vue-tsc && vite build", "preview": "vite preview", "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs,.ts,.tsx,.cts,.mts --fix --ignore-path .gitignore", "type-check": "vue-tsc --noEmit"}, "dependencies": {"vue": "^3.4.0", "vue-router": "^4.2.5", "pinia": "^2.1.7", "axios": "^1.6.2", "tdesign-vue-next": "^1.7.1", "tdesign-icons-vue-next": "^0.2.2", "echarts": "^5.4.3", "vue-echarts": "^6.6.1", "dayjs": "^1.11.10"}, "devDependencies": {"@types/node": "^20.10.5", "@typescript-eslint/eslint-plugin": "^6.14.0", "@typescript-eslint/parser": "^6.14.0", "@vitejs/plugin-vue": "^4.5.2", "@vue/eslint-config-prettier": "^8.0.0", "@vue/eslint-config-typescript": "^12.0.0", "@vue/tsconfig": "^0.5.1", "eslint": "^8.56.0", "eslint-plugin-vue": "^9.19.2", "prettier": "^3.1.1", "typescript": "~5.3.3", "vite": "^5.0.10", "vue-tsc": "^1.8.25"}}