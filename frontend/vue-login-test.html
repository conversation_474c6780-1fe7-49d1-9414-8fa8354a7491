<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Vue Login Test</title>
    <script src="https://unpkg.com/vue@3/dist/vue.global.js"></script>
    <script src="https://unpkg.com/axios/dist/axios.min.js"></script>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
        }

        .form-group {
            margin: 10px 0;
        }

        label {
            display: block;
            margin-bottom: 5px;
        }

        input {
            width: 300px;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }

        button {
            padding: 10px 20px;
            background: #007bff;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }

        button:disabled {
            background: #ccc;
        }

        .result {
            margin-top: 20px;
            padding: 10px;
            border-radius: 4px;
        }

        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
    </style>
</head>

<body>
    <div id="app">
        <h1>Vue 登录测试</h1>
        <form @submit.prevent="handleLogin">
            <div class="form-group">
                <label>用户名:</label>
                <input v-model="username" type="text" required>
            </div>
            <div class="form-group">
                <label>密码:</label>
                <input v-model="password" type="password" required>
            </div>
            <div class="form-group">
                <button type="submit" :disabled="loading">{{ loading ? '登录中...' : '登录' }}</button>
            </div>
        </form>

        <div v-if="result" :class="['result', result.type]">
            <h3>{{ result.title }}</h3>
            <pre>{{ result.content }}</pre>
        </div>
    </div>

    <script>
        const { createApp, ref } = Vue;

        createApp({
            setup() {
                const username = ref('admin');
                const password = ref('admin123');
                const loading = ref(false);
                const result = ref(null);

                const handleLogin = async () => {
                    loading.value = true;
                    result.value = null;

                    try {
                        const response = await axios.post('/api/auth/login', {
                            username: username.value,
                            password: password.value
                        });

                        result.value = {
                            type: 'success',
                            title: '登录成功！',
                            content: JSON.stringify(response.data, null, 2)
                        };

                        // 保存 token
                        if (response.data.data && response.data.data.token) {
                            localStorage.setItem('token', response.data.data.token);
                        }

                    } catch (error) {
                        result.value = {
                            type: 'error',
                            title: '登录失败',
                            content: error.response ?
                                JSON.stringify(error.response.data, null, 2) :
                                error.message
                        };
                    } finally {
                        loading.value = false;
                    }
                };

                return {
                    username,
                    password,
                    loading,
                    result,
                    handleLogin
                };
            }
        }).mount('#app');
    </script>
</body>

</html>