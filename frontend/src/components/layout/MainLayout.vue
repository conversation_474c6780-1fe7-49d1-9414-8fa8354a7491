<template>
  <t-layout class="main-layout">
    <!-- 侧边栏 -->
    <t-aside width="240px" class="sidebar">
      <div class="logo">
        <h2>运维面板</h2>
      </div>

      <t-menu v-model:value="activeMenu" :default-expanded="['containers']" theme="dark" @change="handleMenuChange">
        <t-menu-item value="dashboard">
          <template #icon>
            <dashboard-icon />
          </template>
          仪表板
        </t-menu-item>

        <t-submenu value="containers" title="容器管理">
          <template #icon>
            <server-icon />
          </template>
          <t-menu-item value="containers">容器列表</t-menu-item>
          <t-menu-item value="images">镜像管理</t-menu-item>
          <t-menu-item value="networks">网络管理</t-menu-item>
          <t-menu-item value="volumes">卷管理</t-menu-item>
        </t-submenu>

        <t-menu-item value="databases">
          <template #icon>
            <database-icon />
          </template>
          数据库
        </t-menu-item>

        <t-menu-item value="websites">
          <template #icon>
            <internet-icon />
          </template>
          网站管理
        </t-menu-item>

        <t-menu-item value="files">
          <template #icon>
            <folder-icon />
          </template>
          文件管理
        </t-menu-item>

        <t-menu-item value="monitor">
          <template #icon>
            <chart-line-icon />
          </template>
          系统监控
        </t-menu-item>

        <t-menu-item value="settings">
          <template #icon>
            <setting-icon />
          </template>
          系统设置
        </t-menu-item>
      </t-menu>
    </t-aside>

    <!-- 主内容区 -->
    <t-layout>
      <!-- 顶部导航 -->
      <t-header class="header">
        <div class="header-content">
          <t-breadcrumb>
            <t-breadcrumb-item>首页</t-breadcrumb-item>
            <t-breadcrumb-item v-if="currentRoute.meta?.title">
              {{ currentRoute.meta.title }}
            </t-breadcrumb-item>
          </t-breadcrumb>

          <div class="header-actions">
            <t-dropdown :options="userMenuOptions" @click="handleUserMenuClick">
              <t-button variant="text" class="user-button">
                <user-icon />
                {{ authStore.user?.username }}
                <chevron-down-icon />
              </t-button>
            </t-dropdown>
          </div>
        </div>
      </t-header>

      <!-- 内容区 -->
      <t-content class="content">
        <router-view />
      </t-content>
    </t-layout>
  </t-layout>
</template>

<script setup lang="ts">
import { useAuthStore } from '@/stores/auth'
import {
  ChartLineIcon,
  ChevronDownIcon,
  DashboardIcon,
  FolderIcon,
  InternetIcon,
  ServerIcon,
  SettingIcon,
  UserIcon
} from 'tdesign-icons-vue-next'
import { MessagePlugin } from 'tdesign-vue-next'
import { computed, ref, watch } from 'vue'
import { useRoute, useRouter } from 'vue-router'

const route = useRoute()
const router = useRouter()
const authStore = useAuthStore()

const activeMenu = ref('')
const currentRoute = computed(() => route)

// 用户菜单选项
const userMenuOptions = [
  { content: '个人设置', value: 'profile' },
  { content: '修改密码', value: 'change-password' },
  { content: '退出登录', value: 'logout' }
]

// 监听路由变化，更新活动菜单
watch(
  () => route.name,
  (newName) => {
    if (newName) {
      activeMenu.value = newName.toString().toLowerCase()
    }
  },
  { immediate: true }
)

// 处理菜单点击
const handleMenuChange = (value: string) => {
  router.push(`/${value}`)
}

// 处理用户菜单点击
const handleUserMenuClick = async (data: { value: string }) => {
  switch (data.value) {
    case 'profile':
      // TODO: 打开个人设置对话框
      MessagePlugin.info('个人设置功能开发中')
      break
    case 'change-password':
      // TODO: 打开修改密码对话框
      MessagePlugin.info('修改密码功能开发中')
      break
    case 'logout':
      try {
        await authStore.logout()
        MessagePlugin.success('退出登录成功')
        router.push('/login')
      } catch (error) {
        MessagePlugin.error('退出登录失败')
      }
      break
  }
}

// 初始化认证状态
authStore.initializeAuth()
</script>

<style scoped>
.main-layout {
  height: 100vh;
}

.sidebar {
  background: #001529;
  overflow-y: auto;
}

.logo {
  padding: 16px;
  text-align: center;
  border-bottom: 1px solid #303030;
}

.logo h2 {
  margin: 0;
  color: white;
  font-size: 18px;
  font-weight: 600;
}

.header {
  background: white;
  border-bottom: 1px solid #e5e7eb;
  padding: 0;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 24px;
  height: 100%;
}

.header-actions {
  display: flex;
  align-items: center;
  gap: 16px;
}

.user-button {
  display: flex;
  align-items: center;
  gap: 8px;
}

.content {
  padding: 24px;
  background: #f5f7fa;
  overflow-y: auto;
}
</style>
