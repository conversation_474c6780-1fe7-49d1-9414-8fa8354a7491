<template>
  <div class="container-list">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-left">
        <h2>容器管理</h2>
        <p>管理Docker容器的创建、启动、停止和删除</p>
      </div>
      <div class="header-right">
        <t-button theme="primary" @click="showCreateDialog = true">
          <template #icon><add-icon /></template>
          创建容器
        </t-button>
      </div>
    </div>

    <!-- 搜索和筛选 -->
    <t-card class="filter-card">
      <t-form layout="inline" @submit="handleSearch">
        <t-form-item label="状态">
          <t-select v-model="searchParams.status" placeholder="选择状态" clearable style="width: 120px">
            <t-option value="running" label="运行中" />
            <t-option value="stopped" label="已停止" />
            <t-option value="created" label="已创建" />
          </t-select>
        </t-form-item>

        <t-form-item label="搜索">
          <t-input v-model="searchParams.search" placeholder="搜索容器名称或镜像" clearable style="width: 200px" />
        </t-form-item>

        <t-form-item>
          <t-button type="submit" theme="primary">搜索</t-button>
          <t-button theme="default" @click="handleReset">重置</t-button>
        </t-form-item>
      </t-form>
    </t-card>

    <!-- 容器列表 -->
    <t-card>
      <t-table :data="containers" :columns="columns" :loading="loading" :pagination="pagination"
        @page-change="handlePageChange" @page-size-change="handlePageSizeChange" row-key="id">
        <template #status="{ row }">
          <t-tag :theme="getStatusTheme(row.status)" size="small">
            {{ getStatusText(row.status) }}
          </t-tag>
        </template>

        <template #actions="{ row }">
          <t-space>
            <t-button v-if="row.status === 'stopped'" theme="primary" size="small" @click="handleStart(row)">
              启动
            </t-button>
            <t-button v-if="row.status === 'running'" theme="warning" size="small" @click="handleStop(row)">
              停止
            </t-button>
            <t-button v-if="row.status === 'running'" theme="default" size="small" @click="handleRestart(row)">
              重启
            </t-button>
            <t-button theme="default" size="small" @click="handleViewDetail(row)">
              详情
            </t-button>
            <t-button theme="danger" size="small" @click="handleDelete(row)">
              删除
            </t-button>
          </t-space>
        </template>
      </t-table>
    </t-card>

    <!-- 创建容器对话框 -->
    <t-dialog v-model:visible="showCreateDialog" title="创建容器" width="600px" @confirm="handleCreate"
      @cancel="showCreateDialog = false">
      <t-form ref="createFormRef" :data="createForm" :rules="createRules" layout="vertical">
        <t-form-item label="容器名称" name="name">
          <t-input v-model="createForm.name" placeholder="请输入容器名称" />
        </t-form-item>

        <t-form-item label="镜像" name="image">
          <t-input v-model="createForm.image" placeholder="例如: nginx:latest" />
        </t-form-item>

        <t-form-item label="端口映射">
          <t-input v-model="createForm.ports" placeholder="例如: 80:80,443:443" />
        </t-form-item>

        <t-form-item label="卷挂载">
          <t-input v-model="createForm.volumes" placeholder="例如: /host/path:/container/path" />
        </t-form-item>

        <t-form-item label="环境变量">
          <t-textarea v-model="createForm.environment" placeholder="例如: KEY1=value1,KEY2=value2" />
        </t-form-item>

        <t-form-item label="启动命令">
          <t-input v-model="createForm.command" placeholder="容器启动命令" />
        </t-form-item>
      </t-form>
    </t-dialog>
  </div>
</template>

<script setup lang="ts">
import { containerApi } from '@/api/container'
import type { Container } from '@/types'
import { AddIcon } from 'tdesign-icons-vue-next'
import { DialogPlugin, MessagePlugin } from 'tdesign-vue-next'
import { onMounted, reactive, ref } from 'vue'
import { useRouter } from 'vue-router'

const router = useRouter()

const loading = ref(false)
const containers = ref<Container[]>([])
const showCreateDialog = ref(false)
const createFormRef = ref()

// 搜索参数
const searchParams = reactive({
  status: '',
  search: '',
  page: 1,
  size: 20
})

// 分页配置
const pagination = reactive({
  current: 1,
  pageSize: 20,
  total: 0,
  showJumper: true,
  showSizeChanger: true
})

// 创建表单
const createForm = reactive({
  name: '',
  image: '',
  ports: '',
  volumes: '',
  environment: '',
  command: ''
})

// 创建表单验证规则
const createRules = {
  name: [{ required: true, message: '请输入容器名称' }],
  image: [{ required: true, message: '请输入镜像名称' }]
}

// 表格列配置
const columns = [
  { colKey: 'name', title: '容器名称', width: 150 },
  { colKey: 'image', title: '镜像', width: 200 },
  { colKey: 'status', title: '状态', width: 100 },
  { colKey: 'created_at', title: '创建时间', width: 180 },
  { colKey: 'actions', title: '操作', width: 300, fixed: 'right' }
]

// 获取状态主题
const getStatusTheme = (status: string) => {
  const themes: Record<string, string> = {
    running: 'success',
    stopped: 'danger',
    created: 'warning'
  }
  return themes[status] || 'default'
}

// 获取状态文本
const getStatusText = (status: string) => {
  const texts: Record<string, string> = {
    running: '运行中',
    stopped: '已停止',
    created: '已创建'
  }
  return texts[status] || status
}

// 获取容器列表
const fetchContainers = async () => {
  loading.value = true
  try {
    const response = await containerApi.getContainers(searchParams)
    if (response.data) {
      containers.value = response.data.items
      pagination.total = response.data.total
      pagination.current = response.data.page
    }
  } catch (error: any) {
    MessagePlugin.error(error.message || '获取容器列表失败')
  } finally {
    loading.value = false
  }
}

// 搜索
const handleSearch = () => {
  searchParams.page = 1
  pagination.current = 1
  fetchContainers()
}

// 重置
const handleReset = () => {
  searchParams.status = ''
  searchParams.search = ''
  searchParams.page = 1
  pagination.current = 1
  fetchContainers()
}

// 分页变化
const handlePageChange = (page: number) => {
  searchParams.page = page
  pagination.current = page
  fetchContainers()
}

// 页大小变化
const handlePageSizeChange = (size: number) => {
  searchParams.size = size
  searchParams.page = 1
  pagination.pageSize = size
  pagination.current = 1
  fetchContainers()
}

// 启动容器
const handleStart = async (container: Container) => {
  try {
    await containerApi.startContainer(container.id)
    MessagePlugin.success('容器启动成功')
    fetchContainers()
  } catch (error: any) {
    MessagePlugin.error(error.message || '启动容器失败')
  }
}

// 停止容器
const handleStop = async (container: Container) => {
  try {
    await containerApi.stopContainer(container.id)
    MessagePlugin.success('容器停止成功')
    fetchContainers()
  } catch (error: any) {
    MessagePlugin.error(error.message || '停止容器失败')
  }
}

// 重启容器
const handleRestart = async (container: Container) => {
  try {
    await containerApi.restartContainer(container.id)
    MessagePlugin.success('容器重启成功')
    fetchContainers()
  } catch (error: any) {
    MessagePlugin.error(error.message || '重启容器失败')
  }
}

// 查看详情
const handleViewDetail = (container: Container) => {
  router.push(`/containers/${container.id}`)
}

// 删除容器
const handleDelete = (container: Container) => {
  const dialog = DialogPlugin.confirm({
    header: '确认删除',
    body: `确定要删除容器 "${container.name}" 吗？此操作不可恢复。`,
    onConfirm: async () => {
      try {
        await containerApi.deleteContainer(container.id)
        MessagePlugin.success('容器删除成功')
        fetchContainers()
        dialog.destroy()
      } catch (error: any) {
        MessagePlugin.error(error.message || '删除容器失败')
      }
    }
  })
}

// 创建容器
const handleCreate = async () => {
  const valid = await createFormRef.value?.validate()
  if (valid === true) {
    try {
      await containerApi.createContainer(createForm)
      MessagePlugin.success('容器创建成功')
      showCreateDialog.value = false
      // 重置表单
      Object.assign(createForm, {
        name: '',
        image: '',
        ports: '',
        volumes: '',
        environment: '',
        command: ''
      })
      fetchContainers()
    } catch (error: any) {
      MessagePlugin.error(error.message || '创建容器失败')
    }
  }
}

onMounted(() => {
  fetchContainers()
})
</script>

<style scoped>
.container-list {
  padding: 0;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 24px;
}

.header-left h2 {
  margin: 0 0 8px 0;
  font-size: 24px;
  font-weight: 600;
  color: #1f2937;
}

.header-left p {
  margin: 0;
  color: #6b7280;
  font-size: 14px;
}

.filter-card {
  margin-bottom: 16px;
}
</style>
