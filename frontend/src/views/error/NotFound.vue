<template>
  <div class="not-found">
    <t-card>
      <div class="error-content">
        <h1>404</h1>
        <h2>页面未找到</h2>
        <p>抱歉，您访问的页面不存在。</p>
        <t-button theme="primary" @click="$router.push('/')">
          返回首页
        </t-button>
      </div>
    </t-card>
  </div>
</template>

<script setup lang="ts">
// 404页面
</script>

<style scoped>
.not-found {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 60vh;
}

.error-content {
  text-align: center;
  padding: 40px;
}

.error-content h1 {
  font-size: 72px;
  font-weight: 700;
  color: #e5e7eb;
  margin: 0;
  line-height: 1;
}

.error-content h2 {
  font-size: 24px;
  font-weight: 600;
  color: #374151;
  margin: 16px 0 8px 0;
}

.error-content p {
  color: #6b7280;
  margin: 0 0 24px 0;
}
</style>
