import { http } from './request'
import type { LoginRequest, LoginResponse, User } from '@/types'

export const authApi = {
  // 用户登录
  login(data: LoginRequest) {
    return http.post<LoginResponse>('/auth/login', data)
  },

  // 用户登出
  logout() {
    return http.post('/auth/logout')
  },

  // 刷新token
  refreshToken() {
    return http.post<{ token: string; expires_at: string }>('/auth/refresh')
  },

  // 获取用户信息
  getProfile() {
    return http.get<User>('/auth/profile')
  },

  // 更新用户信息
  updateProfile(data: Partial<User>) {
    return http.put<User>('/auth/profile', data)
  },

  // 修改密码
  changePassword(data: { old_password: string; new_password: string }) {
    return http.post('/auth/change-password', data)
  }
}
