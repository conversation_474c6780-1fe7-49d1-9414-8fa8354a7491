import { http } from './request'
import type { Container, Image, Network, Volume, PaginatedResponse } from '@/types'

export const containerApi = {
  // 容器管理
  getContainers(params?: {
    page?: number
    size?: number
    status?: string
    search?: string
  }) {
    return http.get<PaginatedResponse<Container>>('/containers', { params })
  },

  getContainer(id: number) {
    return http.get<Container>(`/containers/${id}`)
  },

  createContainer(data: {
    name: string
    image: string
    ports?: string
    volumes?: string
    environment?: string
    command?: string
    host_id?: number
  }) {
    return http.post<Container>('/containers', data)
  },

  updateContainer(id: number, data: {
    name: string
    image: string
    ports?: string
    volumes?: string
    environment?: string
    command?: string
    host_id?: number
  }) {
    return http.put<Container>(`/containers/${id}`, data)
  },

  deleteContainer(id: number) {
    return http.delete(`/containers/${id}`)
  },

  startContainer(id: number) {
    return http.post(`/containers/${id}/start`)
  },

  stopContainer(id: number) {
    return http.post(`/containers/${id}/stop`)
  },

  restartContainer(id: number) {
    return http.post(`/containers/${id}/restart`)
  },

  getContainerLogs(id: number) {
    return http.get<string[]>(`/containers/${id}/logs`)
  },

  getContainerStats(id: number) {
    return http.get<{
      cpu_usage: number
      memory_usage: number
      network_io: { rx_bytes: number; tx_bytes: number }
      block_io: { read_bytes: number; write_bytes: number }
    }>(`/containers/${id}/stats`)
  },

  // 镜像管理
  getImages(params?: {
    page?: number
    size?: number
    search?: string
  }) {
    return http.get<PaginatedResponse<Image>>('/images', { params })
  },

  pullImage(data: {
    repository: string
    tag?: string
    host_id?: number
  }) {
    return http.post<Image>('/images', data)
  },

  deleteImage(id: number) {
    return http.delete(`/images/${id}`)
  },

  // 网络管理
  getNetworks() {
    return http.get<Network[]>('/networks')
  },

  createNetwork(data: {
    name: string
    driver?: string
    internal?: boolean
    host_id?: number
  }) {
    return http.post<Network>('/networks', data)
  },

  deleteNetwork(id: number) {
    return http.delete(`/networks/${id}`)
  },

  // 卷管理
  getVolumes() {
    return http.get<Volume[]>('/volumes')
  },

  createVolume(data: {
    name: string
    driver?: string
    host_id?: number
  }) {
    return http.post<Volume>('/volumes', data)
  },

  deleteVolume(id: number) {
    return http.delete(`/volumes/${id}`)
  }
}
