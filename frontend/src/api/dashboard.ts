import { http } from './request'
import type { OverviewData, StatsData, MonitorData } from '@/types'

export const dashboardApi = {
  // 获取系统概览
  getOverview() {
    return http.get<OverviewData>('/dashboard/overview')
  },

  // 获取统计数据
  getStats() {
    return http.get<StatsData>('/dashboard/stats')
  },

  // 获取监控数据
  getMonitorData() {
    return http.get<MonitorData[]>('/dashboard/monitor')
  }
}
