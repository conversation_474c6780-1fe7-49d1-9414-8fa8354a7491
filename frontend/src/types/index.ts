// 基础响应类型
export interface ApiResponse<T = any> {
  code: number
  message: string
  data?: T
  timestamp?: string
}

// 分页响应类型
export interface PaginatedResponse<T = any> {
  items: T[]
  total: number
  page: number
  size: number
  pages: number
}

// 用户类型
export interface User {
  id: number
  username: string
  email: string
  role: 'admin' | 'user' | 'readonly'
  status: 'active' | 'inactive' | 'locked'
  last_login_at?: string
  created_at: string
  updated_at: string
}

// 登录请求类型
export interface LoginRequest {
  username: string
  password: string
}

// 登录响应类型
export interface LoginResponse {
  token: string
  expires_at: string
  user: User
}

// 容器类型
export interface Container {
  id: number
  container_id: string
  name: string
  image: string
  status: string
  ports?: string
  volumes?: string
  environment?: string
  command?: string
  host_id?: number
  host?: Host
  created_at: string
  updated_at: string
}

// 镜像类型
export interface Image {
  id: number
  image_id: string
  repository: string
  tag: string
  size: number
  created_time: string
  host_id?: number
  host?: Host
  created_at: string
  updated_at: string
}

// 网络类型
export interface Network {
  id: number
  network_id: string
  name: string
  driver: string
  scope?: string
  internal: boolean
  ipam?: string
  options?: string
  host_id?: number
  host?: Host
  created_at: string
  updated_at: string
}

// 卷类型
export interface Volume {
  id: number
  volume_id: string
  name: string
  driver: string
  mountpoint?: string
  options?: string
  labels?: string
  host_id?: number
  host?: Host
  created_at: string
  updated_at: string
}

// 主机类型
export interface Host {
  id: number
  name: string
  address: string
  port: number
  username?: string
  auth_method: 'password' | 'key'
  group_id?: number
  status: string
  description?: string
  group?: HostGroup
  created_at: string
  updated_at: string
}

// 主机分组类型
export interface HostGroup {
  id: number
  name: string
  description?: string
  hosts?: Host[]
  created_at: string
  updated_at: string
}

// 数据库类型
export interface Database {
  id: number
  name: string
  type: 'mysql' | 'postgresql' | 'redis' | 'mongodb'
  host: string
  port: number
  username?: string
  database_name?: string
  charset?: string
  collation?: string
  status: string
  description?: string
  users?: DatabaseUser[]
  created_at: string
  updated_at: string
}

// 数据库用户类型
export interface DatabaseUser {
  id: number
  database_id: number
  username: string
  host: string
  privileges?: string
  database?: Database
  created_at: string
  updated_at: string
}

// 网站类型
export interface Website {
  id: number
  name: string
  primary_domain: string
  document_root: string
  php_version?: string
  status: string
  ssl_enabled: boolean
  ssl_cert_id?: number
  description?: string
  domains?: Domain[]
  ssl_certificate?: SSLCertificate
  created_at: string
  updated_at: string
}

// 域名类型
export interface Domain {
  id: number
  website_id: number
  domain: string
  type: 'primary' | 'alias' | 'redirect'
  target?: string
  status: string
  website?: Website
  created_at: string
  updated_at: string
}

// SSL证书类型
export interface SSLCertificate {
  id: number
  name: string
  domain: string
  type: 'self_signed' | 'lets_encrypt' | 'custom'
  expires_at: string
  status: string
  auto_renew: boolean
  websites?: Website[]
  created_at: string
  updated_at: string
}

// 应用类型
export interface Application {
  id: number
  name: string
  key: string
  version?: string
  category?: string
  description?: string
  icon?: string
  docker_compose?: string
  install_params?: string
  status: string
  instances?: ApplicationInstance[]
  created_at: string
  updated_at: string
}

// 应用实例类型
export interface ApplicationInstance {
  id: number
  application_id: number
  name: string
  version?: string
  status: string
  install_path?: string
  config?: string
  environment?: string
  ports?: string
  application?: Application
  created_at: string
  updated_at: string
}

// 任务类型
export interface Task {
  id: number
  name: string
  type: string
  status: 'pending' | 'running' | 'completed' | 'failed'
  progress: number
  message?: string
  params?: string
  result?: string
  user_id: number
  started_at?: string
  completed_at?: string
  user?: User
  logs?: TaskLog[]
  created_at: string
  updated_at: string
}

// 任务日志类型
export interface TaskLog {
  id: number
  task_id: number
  level: string
  message: string
  task?: Task
  created_at: string
  updated_at: string
}

// 系统监控数据类型
export interface MonitorData {
  timestamp: number
  cpu_usage: number
  memory_usage: number
  disk_usage: number
  network_in: number
  network_out: number
}

// 系统概览数据类型
export interface OverviewData {
  system_info: {
    os: string
    arch: string
    go_version: string
    num_cpu: number
    num_goroutine: number
  }
  resource_stats: {
    cpu_usage: number
    memory_usage: number
    disk_usage: number
    network_io: {
      bytes_received: number
      bytes_sent: number
    }
  }
  service_stats: {
    container_count: number
    image_count: number
    network_count: number
    volume_count: number
    database_count: number
    website_count: number
  }
}

// 统计数据类型
export interface StatsData {
  total_users: number
  active_containers: number
  total_images: number
  total_websites: number
  total_databases: number
  total_tasks: number
  completed_tasks: number
  failed_tasks: number
}
