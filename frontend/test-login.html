<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Login Test</title>
</head>

<body>
    <h1>登录测试</h1>
    <form id="loginForm">
        <div>
            <label>用户名:</label>
            <input type="text" id="username" value="admin" required>
        </div>
        <div>
            <label>密码:</label>
            <input type="password" id="password" value="admin123" required>
        </div>
        <div>
            <button type="submit">登录</button>
        </div>
    </form>

    <div id="result"></div>

    <script>
        document.getElementById('loginForm').addEventListener('submit', async (e) => {
            e.preventDefault();

            const username = document.getElementById('username').value;
            const password = document.getElementById('password').value;
            const resultDiv = document.getElementById('result');

            try {
                const response = await fetch('/api/auth/login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ username, password })
                });

                const data = await response.json();

                if (response.ok) {
                    resultDiv.innerHTML = `
                        <h3>登录成功！</h3>
                        <p>Token: ${data.data.token}</p>
                        <p>用户: ${data.data.user.username}</p>
                        <p>角色: ${data.data.user.role}</p>
                    `;
                    resultDiv.style.color = 'green';
                } else {
                    resultDiv.innerHTML = `<h3>登录失败：${data.message}</h3>`;
                    resultDiv.style.color = 'red';
                }
            } catch (error) {
                resultDiv.innerHTML = `<h3>请求错误：${error.message}</h3>`;
                resultDiv.style.color = 'red';
            }
        });
    </script>
</body>

</html>