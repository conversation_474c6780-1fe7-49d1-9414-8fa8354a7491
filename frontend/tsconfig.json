{"extends": "@vue/tsconfig/tsconfig.dom.json", "include": ["env.d.ts", "src/**/*", "src/**/*.vue"], "exclude": ["src/**/__tests__/*"], "compilerOptions": {"composite": true, "baseUrl": ".", "paths": {"@/*": ["./src/*"], "@/components/*": ["./src/components/*"], "@/views/*": ["./src/views/*"], "@/api/*": ["./src/api/*"], "@/utils/*": ["./src/utils/*"], "@/stores/*": ["./src/stores/*"], "@/types/*": ["./src/types/*"], "@/router/*": ["./src/router/*"]}, "types": ["vite/client"]}}