# VS Code 调试配置说明

本项目已为前后端开发配置了完整的VS Code调试环境。

## 🚀 快速开始

### 方式一：使用调试面板（推荐）
1. 按 `F5` 或点击左侧调试图标
2. 选择 "🚀 启动全栈开发环境" 
3. 这将同时启动后端Go服务器和前端Vue开发服务器

### 方式二：使用任务面板
1. 按 `Cmd+Shift+P` 打开命令面板
2. 输入 "Tasks: Run Task"
3. 选择 "🚀 启动全栈开发环境"

## 📋 可用的调试配置

### 后端调试配置
- **启动后端服务器**: 启动Go后端服务器进行调试
- **调试后端测试**: 运行并调试Go测试
- **附加到运行中的后端进程**: 附加到已运行的后端进程（需要先用delve启动）

### 前端调试配置
- **启动前端开发服务器**: 启动Vite开发服务器
- **Chrome调试前端**: 在Chrome中调试前端应用
- **Edge调试前端**: 在Edge中调试前端应用

### 组合配置
- **🚀 启动全栈开发环境**: 同时启动前后端服务器
- **🌐 全栈+浏览器调试**: 启动全栈环境并打开Chrome调试

## 🛠️ 可用的任务

### 后端任务
- `构建后端`: 编译Go后端
- `运行后端`: 运行Go后端服务器
- `测试后端`: 运行后端测试

### 前端任务
- `安装前端依赖`: 安装npm依赖
- `启动前端开发服务器`: 启动Vite开发服务器
- `构建前端`: 构建生产版本
- `前端类型检查`: 运行TypeScript类型检查
- `前端代码检查`: 运行ESLint代码检查

## 🔧 断点调试

### Go后端断点
1. 在Go代码中设置断点（点击行号左侧）
2. 使用 "启动后端服务器" 配置启动调试
3. 发送HTTP请求触发断点

### Vue前端断点
1. 在Vue/TypeScript代码中设置断点
2. 使用 "Chrome调试前端" 或 "Edge调试前端" 配置
3. 在浏览器中操作应用触发断点

## 📦 推荐的扩展

项目已配置了推荐的VS Code扩展，首次打开项目时会提示安装：

### 必需扩展
- **Go**: Go语言支持
- **Vue Language Features (Volar)**: Vue 3支持
- **TypeScript Vue Plugin**: Vue中的TypeScript支持
- **Prettier**: 代码格式化
- **ESLint**: 代码检查

### 推荐扩展
- **Docker**: Docker支持
- **TODO Tree**: TODO高亮
- **Path Intellisense**: 路径智能提示
- **Material Icon Theme**: 文件图标主题
- **GitHub Copilot**: AI代码助手

## ⚙️ 项目设置

### Go设置
- 自动格式化代码（保存时）
- 使用goimports格式化
- 启用golangci-lint代码检查
- 测试超时时间：30秒

### TypeScript/Vue设置
- 自动导入优化
- 保存时格式化代码
- ESLint自动修复
- Vue文件格式化支持

## 🐛 调试技巧

### 后端调试
```go
// 在代码中添加日志
hlog.Info("Debug info", "key", value)

// 使用断点查看变量
// 在调试控制台中输入变量名查看值
```

### 前端调试
```typescript
// 在代码中添加断点
debugger;

// 使用console调试
console.log('Debug info:', variable);

// 在浏览器开发者工具中查看网络请求
```

## 🔍 问题排查

### 后端启动失败
1. 检查Go版本是否为1.21+
2. 确保在backend目录下有go.mod文件
3. 检查配置文件是否正确

### 前端启动失败
1. 确保已安装Node.js 16+
2. 运行 `npm install` 安装依赖
3. 检查端口3000是否被占用

### 调试器无法连接
1. 确保防火墙未阻止端口
2. 检查调试配置中的端口设置
3. 重启VS Code并重新启动调试

## 📖 更多信息

- [Go调试文档](https://github.com/golang/vscode-go/blob/master/docs/debugging.md)
- [Vue.js调试指南](https://vuejs.org/guide/scaling-up/testing.html#debugging)
- [VS Code调试文档](https://code.visualstudio.com/docs/editor/debugging)
