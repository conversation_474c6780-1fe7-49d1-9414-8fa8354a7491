{"go.toolsManagement.autoUpdate": true, "go.useLanguageServer": true, "go.gopath": "", "go.goroot": "", "go.lintTool": "golangci-lint", "go.lintOnSave": "package", "go.buildOnSave": "off", "go.vetOnSave": "package", "go.formatTool": "goimports", "go.testFlags": ["-v"], "go.testTimeout": "30s", "go.coverOnSave": false, "go.coverageDecorator": {"type": "gutter", "coveredHighlightColor": "rgba(64,128,128,0.5)", "uncoveredHighlightColor": "rgba(128,64,64,0.25)", "coveredGutterStyle": "blockblue", "uncoveredGutterStyle": "blockred"}, "typescript.preferences.includePackageJsonAutoImports": "auto", "typescript.suggest.autoImports": true, "typescript.updateImportsOnFileMove.enabled": "always", "eslint.validate": ["javascript", "javascriptreact", "typescript", "typescriptreact", "vue"], "eslint.format.enable": true, "vetur.format.defaultFormatter.html": "prettier", "vetur.format.defaultFormatter.css": "prettier", "vetur.format.defaultFormatter.js": "prettier", "vetur.format.defaultFormatter.ts": "prettier", "editor.formatOnSave": true, "editor.codeActionsOnSave": {"source.fixAll.eslint": "explicit", "source.organizeImports": "explicit"}, "files.associations": {"*.vue": "vue", "*.go": "go"}, "emmet.includeLanguages": {"vue-html": "html", "vue": "html"}, "debug.console.fontSize": 14, "debug.console.lineHeight": 18, "debug.inlineValues": "auto", "debug.showBreakpointsInOverviewRuler": true, "[go]": {"editor.formatOnSave": true, "editor.insertSpaces": false, "editor.codeActionsOnSave": {"source.organizeImports": "explicit"}}, "[javascript]": {"editor.defaultFormatter": "esbenp.prettier-vscode", "editor.formatOnSave": true}, "[typescript]": {"editor.defaultFormatter": "esbenp.prettier-vscode", "editor.formatOnSave": true}, "[vue]": {"editor.defaultFormatter": "Vue.volar", "editor.formatOnSave": true}, "[json]": {"editor.defaultFormatter": "esbenp.prettier-vscode", "editor.formatOnSave": true}, "files.exclude": {"**/node_modules": true, "**/dist": true, "**/build": true, "**/.git": true, "**/.svn": true, "**/.hg": true, "**/CVS": true, "**/.DS_Store": true, "**/Thumbs.db": true}, "search.exclude": {"**/node_modules": true, "**/bower_components": true, "**/dist": true, "**/build": true, "**/.git": true}, "files.watcherExclude": {"**/node_modules/**": true, "**/dist/**": true, "**/build/**": true}}