{"version": "2.0.0", "tasks": [{"label": "构建后端", "type": "shell", "command": "go", "args": ["build", "-o", "main", "./cmd/server"], "group": "build", "options": {"cwd": "${workspaceFolder}/backend"}, "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "showReuseMessage": true, "clear": false}, "problemMatcher": "$go"}, {"label": "运行后端", "type": "shell", "command": "go", "args": ["run", "./cmd/server"], "group": "build", "options": {"cwd": "${workspaceFolder}/backend"}, "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "showReuseMessage": true, "clear": false}, "problemMatcher": "$go", "isBackground": true, "runOptions": {"instanceLimit": 1}}, {"label": "测试后端", "type": "shell", "command": "go", "args": ["test", "-v", "./..."], "group": "test", "options": {"cwd": "${workspaceFolder}/backend"}, "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "showReuseMessage": true, "clear": false}, "problemMatcher": "$go"}, {"label": "安装前端依赖", "type": "shell", "command": "npm", "args": ["install"], "group": "build", "options": {"cwd": "${workspaceFolder}/frontend"}, "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "showReuseMessage": true, "clear": false}, "problemMatcher": []}, {"label": "启动前端开发服务器", "type": "shell", "command": "npm", "args": ["run", "dev"], "group": "build", "options": {"cwd": "${workspaceFolder}/frontend"}, "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "showReuseMessage": true, "clear": false}, "isBackground": true, "problemMatcher": {"owner": "vite", "pattern": {"regexp": "^(.*):(\\d+):(\\d+):\\s+(warning|error):\\s+(.*)$", "file": 1, "line": 2, "column": 3, "severity": 4, "message": 5}, "background": {"activeOnStart": true, "beginsPattern": ".*Local:.*", "endsPattern": ".*ready in.*"}}, "runOptions": {"instanceLimit": 1}}, {"label": "构建前端", "type": "shell", "command": "npm", "args": ["run", "build"], "group": "build", "options": {"cwd": "${workspaceFolder}/frontend"}, "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "showReuseMessage": true, "clear": false}, "problemMatcher": []}, {"label": "前端类型检查", "type": "shell", "command": "npm", "args": ["run", "type-check"], "group": "test", "options": {"cwd": "${workspaceFolder}/frontend"}, "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "showReuseMessage": true, "clear": false}, "problemMatcher": "$tsc"}, {"label": "前端代码检查", "type": "shell", "command": "npm", "args": ["run", "lint"], "group": "test", "options": {"cwd": "${workspaceFolder}/frontend"}, "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "showReuseMessage": true, "clear": false}, "problemMatcher": "$eslint-stylish"}, {"label": "🚀 启动全栈开发环境", "dependsOrder": "parallel", "dependsOn": ["运行后端", "启动前端开发服务器"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "showReuseMessage": true, "clear": false}}, {"label": "停止所有开发服务器", "type": "shell", "command": "echo", "args": ["停止开发服务器 - 请手动终止正在运行的任务"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "showReuseMessage": true, "clear": false}}]}