{"version": "0.2.0", "configurations": [{"name": "启动后端服务器", "type": "go", "request": "launch", "mode": "auto", "program": "${workspaceFolder}/backend/cmd/server", "cwd": "${workspaceFolder}/backend", "env": {"GO_ENV": "development"}, "console": "integratedTerminal", "stopOnEntry": false, "showLog": true, "trace": "verbose", "logOutput": "rpc"}, {"name": "调试后端测试", "type": "go", "request": "launch", "mode": "test", "program": "${workspaceFolder}/backend", "cwd": "${workspaceFolder}/backend", "env": {"GO_ENV": "test"}, "console": "integratedTerminal"}, {"name": "附加到运行中的后端进程", "type": "go", "request": "attach", "mode": "remote", "remotePath": "${workspaceFolder}/backend", "port": 2345, "host": "127.0.0.1", "showLog": true, "trace": "verbose", "logOutput": "rpc"}, {"name": "启动前端开发服务器", "type": "node", "request": "launch", "program": "${workspaceFolder}/frontend/node_modules/.bin/vite", "args": ["--host", "0.0.0.0", "--port", "3000"], "cwd": "${workspaceFolder}/frontend", "console": "integratedTerminal", "env": {"NODE_ENV": "development"}, "resolveSourceMapLocations": ["${workspaceFolder}/frontend/**", "!**/node_modules/**"], "skipFiles": ["<node_internals>/**", "**/node_modules/**"]}, {"name": "Chrome调试前端", "type": "chrome", "request": "launch", "url": "http://localhost:3000", "webRoot": "${workspaceFolder}/frontend/src", "sourceMaps": true, "userDataDir": "${workspaceFolder}/.vscode/chrome-debug-profile", "runtimeArgs": ["--disable-web-security", "--disable-features=VizDisplayCompositor"]}, {"name": "Edge调试前端", "type": "msedge", "request": "launch", "url": "http://localhost:3000", "webRoot": "${workspaceFolder}/frontend/src", "sourceMaps": true, "userDataDir": "${workspaceFolder}/.vscode/edge-debug-profile"}], "compounds": [{"name": "🚀 启动全栈开发环境", "configurations": ["启动后端服务器", "启动前端开发服务器"], "stopAll": true, "presentation": {"hidden": false, "group": "development", "order": 1}}, {"name": "🌐 全栈+浏览器调试", "configurations": ["启动后端服务器", "启动前端开发服务器", "Chrome调试前端"], "stopAll": true, "presentation": {"hidden": false, "group": "development", "order": 2}}]}