{"Go Handler Function": {"prefix": "gohandler", "body": ["func ${1:HandlerName}(ctx context.Context, c *app.RequestContext) {", "\t// TODO: 实现处理逻辑", "\t$0", "\tc.<PERSON>(http.StatusOK, map[string]interface{}{", "\t\t\"code\": 0,", "\t\t\"message\": \"success\",", "\t\t\"data\": nil,", "\t})", "}"], "description": "创建Go处理函数模板"}, "Go Middleware": {"prefix": "gomiddleware", "body": ["func ${1:MiddlewareName}() app.HandlerFunc {", "\treturn func(ctx context.Context, c *app.RequestContext) {", "\t\t// TODO: 实现中间件逻辑", "\t\t$0", "\t\tc.Next(ctx)", "\t}", "}"], "description": "创建Go中间件模板"}, "Go Error Response": {"prefix": "goerror", "body": ["c.<PERSON>(http.Status${1:BadRequest}, map[string]interface{}{", "\t\"code\": ${2:400},", "\t\"message\": \"${3:error message}\",", "\t\"data\": nil,", "})", "return"], "description": "创建Go错误响应"}, "Go Success Response": {"prefix": "gosuccess", "body": ["c.<PERSON>(http.StatusOK, map[string]interface{}{", "\t\"code\": 0,", "\t\"message\": \"success\",", "\t\"data\": ${1:data},", "})"], "description": "创建Go成功响应"}, "Vue Component": {"prefix": "vuecomp", "body": ["<template>", "\t<div class=\"${1:component-name}\">", "\t\t$0", "\t</div>", "</template>", "", "<script setup lang=\"ts\">", "import { ref, reactive, onMounted } from 'vue'", "", "// Props定义", "interface Props {", "\t// TODO: 定义props类型", "}", "", "const props = defineProps<Props>()", "", "// 响应式数据", "const state = reactive({", "\t// TODO: 定义状态", "})", "", "// 生命周期", "onMounted(() => {", "\t// TODO: 组件挂载后的逻辑", "})", "</script>", "", "<style scoped>", ".${1:component-name} {", "\t/* TODO: 添加样式 */", "}", "</style>"], "description": "创建Vue组件模板"}, "Vue API Call": {"prefix": "v<PERSON><PERSON>i", "body": ["const ${1:functionName} = async (${2:params}) => {", "\ttry {", "\t\tconst response = await ${3:apiMethod}(${2:params})", "\t\tif (response.code === 0) {", "\t\t\t// 成功处理", "\t\t\t$0", "\t\t} else {", "\t\t\t// 错误处理", "\t\t\tconsole.error('API Error:', response.message)", "\t\t}", "\t} catch (error) {", "\t\tconsole.error('Request failed:', error)", "\t}", "}"], "description": "创建Vue API调用函数"}, "API Function": {"prefix": "apifunction", "body": ["export const ${1:functionName} = (${2:params}: ${3:ParamsType}) => {", "\treturn request<${4:ResponseType}>({", "\t\turl: '${5:/api/endpoint}',", "\t\tmethod: '${6|get,post,put,delete|}',", "\t\t${7:data: params}", "\t})", "}"], "description": "创建API函数"}, "Pinia Store": {"prefix": "piniastore", "body": ["import { defineStore } from 'pinia'", "import { ref, computed } from 'vue'", "", "export const use${1:StoreName}Store = defineStore('${2:storeName}', () => {", "\t// State", "\tconst ${3:state} = ref(${4:initialValue})", "", "\t// <PERSON><PERSON>", "\tconst ${5:getter} = computed(() => {", "\t\treturn ${3:state}.value", "\t})", "", "\t// Actions", "\tconst ${6:action} = async (${7:params}) => {", "\t\t// TODO: 实现action逻辑", "\t\t$0", "\t}", "", "\treturn {", "\t\t${3:state},", "\t\t${5:getter},", "\t\t${6:action}", "\t}", "})"], "description": "创建Pinia Store"}}