wX      
                    #ifdef __clang__
                    #pragma clang diagnostic ignored "-Wall"
                    #endif
                #include <metal_stdlib>
using namespace metal;

template<typename T, size_t N>
struct tint_array {
  const constant T& operator[](size_t i) const constant { return elements[i]; }
  device T& operator[](size_t i) device { return elements[i]; }
  const device T& operator[](size_t i) const device { return elements[i]; }
  thread T& operator[](size_t i) thread { return elements[i]; }
  const thread T& operator[](size_t i) const thread { return elements[i]; }
  threadgroup T& operator[](size_t i) threadgroup { return elements[i]; }
  const threadgroup T& operator[](size_t i) const threadgroup { return elements[i]; }
  T elements[N];
};

struct tint_struct {
  float4 tint_member;
  uint2 tint_member_1;
  float2 tint_member_2;
  float4 tint_member_3;
  float4 tint_member_4;
  float4 tint_member_5;
  float4 tint_member_6;
  float2 tint_member_7;
  float2 tint_member_8;
};

struct tint_struct_1 {
  float4 tint_member_9;
};

struct tint_struct_5 {
  /* 0x0000 */ packed_float3 tint_member_21;
  /* 0x000c */ tint_array<int8_t, 4> tint_pad_1;
};

struct tint_struct_4 {
  /* 0x0000 */ float4x4 tint_member_12;
  /* 0x0040 */ tint_array<float4, 4> tint_member_13;
  /* 0x0080 */ float4 tint_member_14;
  /* 0x0090 */ float tint_member_15;
  /* 0x0094 */ float tint_member_16;
  /* 0x0098 */ int tint_member_17;
  /* 0x009c */ int tint_member_18;
  /* 0x00a0 */ int tint_member_19;
  /* 0x00a4 */ tint_array<int8_t, 12> tint_pad;
  /* 0x00b0 */ tint_array<tint_struct_5, 3> tint_member_20;
  /* 0x00e0 */ float4 tint_member_22;
  /* 0x00f0 */ float4 tint_member_23;
  /* 0x0100 */ float4 tint_member_24;
  /* 0x0110 */ float4 tint_member_25;
  /* 0x0120 */ float tint_member_26;
  /* 0x0124 */ tint_array<int8_t, 12> tint_pad_2;
};

struct tint_struct_3 {
  /* 0x0000 */ tint_array<tint_struct_4, 1> tint_member_11;
};

struct tint_struct_2 {
  const device tint_struct_3* tint_member_10;
  thread uint* tint_member_27;
  sampler tint_member_28;
  texture2d<float, access::sample> tint_member_29;
};

struct tint_struct_6 {
  float4 tint_member_30 [[color(0)]];
};

struct tint_struct_7 {
  uint2 tint_member_31 [[user(locn0)]] [[flat]];
  float2 tint_member_32 [[user(locn1)]];
  float4 tint_member_33 [[user(locn2)]];
  float4 tint_member_34 [[user(locn3)]];
  float4 tint_member_35 [[user(locn4)]];
  float4 tint_member_36 [[user(locn5)]];
  float2 tint_member_37 [[user(locn6)]];
  float2 tint_member_38 [[user(locn7)]];
};

void v(thread float2* const v_1, float2x2 v_2, float2 v_3, float2 v_4, float2 v_5, float2 v_6) {
  float2 const v_7 = (v_6 - v_4);
  bool const v_8 = all((v_7 > float2(0.0f)));
  if (v_8) {
    bool const v_9 = all((v_6 > float2(0.0f)));
    bool v_10 = false;
    if (v_9) {
      v_10 = true;
    } else {
      bool v_11 = false;
      if ((v_3.x > 0.0f)) {
        v_11 = (v_3.y < 0.0f);
      } else {
        v_11 = false;
      }
      v_10 = v_11;
    }
    if (v_10) {
      float2 const v_12 = (v_7 * v_5);
      float2 const v_13 = (1.0f / ((v_6 * v_6) + (v_3.x * v_3.x)));
      float2 const v_14 = (v_13 * v_12);
      float2 const v_15 = (v_14 * v_2);
      float const v_16 = dot(v_15, v_15);
      float const v_17 = rsqrt(v_16);
      float const v_18 = v_17;
      float const v_19 = dot(v_12, v_14);
      float const v_20 = ((0.5f * v_18) * (v_19 - 1.0f));
      float const v_21 = (((v_6.x * v_3.x) * v_13.x) * v_18);
      float2 v_22 = float2((v_21 - v_20), (v_21 + v_20));
      v_22.y = select(-(v_22.y), 1.0f, ((v_6.x - v_3.x) <= 0.0f));
      float2 const v_23 = min((*v_1), v_22);
      (*v_1) = v_23;
    } else {
      if ((v_3.y == 0.0f)) {
        float2 const v_24 = (v_5 * v_2);
        float const v_25 = dot(v_24, v_24);
        float const v_26 = rsqrt(v_25);
        float const v_27 = (((v_3.x - v_7.x) - v_7.y) * v_26);
        float const v_28 = min((*v_1).x, v_27);
        (*v_1).x = v_28;
      }
    }
  }
}

float3 v_29(float3 v_30) {
  float const v_31 = (v_30.x * 0.01745329238474369049f);
  float const v_32 = cos(v_31);
  float const v_33 = (v_30.x * 0.01745329238474369049f);
  float const v_34 = sin(v_33);
  return float3(v_30.z, (v_30.y * v_32), (v_30.y * v_34));
}

float3 v_35(float3 v_36) {
  float3 v_37 = v_36;
  float const v_38 = v_37.x;
  float const v_39 = (v_38 - (360.0f * floor((v_38 / 360.0f))));
  v_37.x = v_39;
  if ((v_37.x < 0.0f)) {
    v_37.x = (v_37.x + 360.0f);
  }
  v_37 = float3(v_37.x, (v_37.yz * 0.00999999977648258209f));
  float3 const v_40 = (float3(0.0f, 8.0f, 4.0f) + (v_37.x * 0.03333333507180213928f));
  float3 const v_41 = (v_40 - (12.0f * floor((v_40 / 12.0f))));
  float3 const v_42 = v_41;
  float const v_43 = min(v_37.z, (1.0f - v_37.z));
  float const v_44 = (v_37.y * v_43);
  float3 const v_45 = min((v_42 - 3.0f), (9.0f - v_42));
  float3 const v_46 = clamp(v_45, float3(-1.0f), float3(1.0f));
  return (v_37.z - (v_44 * v_46));
}

float3 v_47(float3 v_48) {
  float3 v_49 = 0.0f;
  v_49.y = ((v_48.x + 16.0f) * 0.00862068962305784225f);
  v_49.x = ((v_48.y * 0.00200000009499490261f) + v_49.y);
  v_49.z = (v_49.y - (v_48.z * 0.00499999988824129105f));
  float3 const v_50 = powr(v_49, float3(3.0f));
  float3 const v_51 = v_50;
  float v_52 = 0.0f;
  if ((v_51.x > 0.00885645207017660141f)) {
    v_52 = v_51.x;
  } else {
    v_52 = (((116.0f * v_49.x) - 16.0f) * 0.00110705639235675335f);
  }
  float v_53 = 0.0f;
  if ((v_48.x > 8.00000095367431640625f)) {
    v_53 = v_51.y;
  } else {
    v_53 = (v_48.x * 0.00110705639235675335f);
  }
  float v_54 = 0.0f;
  if ((v_51.z > 0.00885645207017660141f)) {
    v_54 = v_51.z;
  } else {
    v_54 = (((116.0f * v_49.z) - 16.0f) * 0.00110705639235675335f);
  }
  float3 const v_55 = float3(v_52, v_53, v_54);
  return (v_55 * float3(0.96429562568664550781f, 1.0f, 0.82510453462600708008f));
}

float3 v_56(float3 v_57) {
  float const v_58 = ((v_57.x + (0.39633777737617492676f * v_57.y)) + (0.21580375730991363525f * v_57.z));
  float const v_59 = ((v_57.x - (0.10556134581565856934f * v_57.y)) - (0.06385417282581329346f * v_57.z));
  float const v_60 = ((v_57.x - (0.08948417752981185913f * v_57.y)) - (1.29148554801940917969f * v_57.z));
  float const v_61 = ((v_58 * v_58) * v_58);
  float const v_62 = ((v_59 * v_59) * v_59);
  float const v_63 = ((v_60 * v_60) * v_60);
  return float3((((4.07674169540405273438f * v_61) - (3.30771160125732421875f * v_62)) + (0.23096993565559387207f * v_63)), (((-1.26843798160552978516f * v_61) + (2.60975742340087890625f * v_62)) - (0.3413193821907043457f * v_63)), (((-0.00419608643278479576f * v_61) - (0.70341861248016357422f * v_62)) + (1.70761466026306152344f * v_63)));
}

float3 v_64(float3 v_65) {
  float3 v_66 = v_65;
  float2 const v_67 = v_66.yz;
  float v_68 = 0.0f;
  float2 v_69 = 0.0f;
  float2 v_70 = 0.0f;
  float const v_71 = dot(v_67, float2(0.40970200300216674805f, -0.91221898794174194336f));
  if ((v_71 < 0.0f)) {
    float const v_72 = dot(v_67, float2(0.46027600765228271484f, 0.88777601718902587891f));
    if ((v_72 < 0.0f)) {
      float const v_73 = dot(v_67, float2(-0.17112199962139129639f, 0.98524999618530273438f));
      if ((v_73 < 0.0f)) {
        v_68 = 0.1020469963550567627f;
        v_69 = float2(-0.01480400003492832184f, -0.16260799765586853027f);
        v_70 = float2(-0.27678599953651428223f, 0.00419300002977252007f);
      } else {
        v_68 = 0.09202899783849716187f;
        v_69 = float2(-0.03853299841284751892f, -0.00164999999105930328f);
        v_70 = float2(-0.23257200419902801514f, -0.09433099627494812012f);
      }
    } else {
      float const v_74 = dot(v_67, float2(0.94792497158050537109f, 0.31849500536918640137f));
      if ((v_74 < 0.0f)) {
        v_68 = 0.08170899748802185059f;
        v_69 = float2(-0.03460099920630455017f, -0.0022150001022964716f);
        v_70 = float2(0.01218499988317489624f, 0.33803099393844604492f);
      } else {
        v_68 = 0.09113200008869171143f;
        v_69 = float2(0.07037000358104705811f, 0.03413899987936019897f);
        v_70 = float2(0.01816999912261962891f, 0.37854999303817749023f);
      }
    }
  } else {
    float const v_75 = dot(v_67, float2(-0.9067999720573425293f, 0.42156198620796203613f));
    if ((v_75 < 0.0f)) {
      float const v_76 = dot(v_67, float2(-0.39791899919509887695f, -0.91742098331451416016f));
      if ((v_76 < 0.0f)) {
        v_68 = 0.11390200257301330566f;
        v_69 = float2(0.09083600342273712158f, 0.03625100106000900269f);
        v_70 = float2(0.22678099572658538818f, 0.01876400038599967957f);
      } else {
        v_68 = 0.1617390066385269165f;
        v_69 = float2(-0.00820199958980083466f, -0.26481899619102478027f);
        v_70 = float2(0.18715600669384002686f, -0.28430399298667907715f);
      }
    } else {
      v_68 = 0.1020469963550567627f;
      v_69 = float2(-0.01480400003492832184f, -0.16260799765586853027f);
      v_70 = float2(-0.27678599953651428223f, 0.00419300002977252007f);
    }
  }
  float v_77 = 1.0f;
  float const v_78 = dot(v_69, v_67);
  float const v_79 = v_78;
  if ((v_79 > 0.0f)) {
    float const v_80 = (1.0f - v_66.x);
    float const v_81 = (v_68 * v_80);
    if ((v_81 < v_79)) {
      float const v_82 = min(v_77, (v_81 / v_79));
      v_77 = v_82;
    }
  }
  float const v_83 = dot(v_70, v_67);
  float const v_84 = v_83;
  if ((v_84 > 0.0f)) {
    float const v_85 = v_66.x;
    float const v_86 = (v_68 * v_85);
    if ((v_86 < v_84)) {
      float const v_87 = min(v_77, (v_86 / v_84));
      v_77 = v_87;
    }
  }
  v_66 = float3(v_66.x, (v_66.yz * v_77));
  float3 const v_88 = v_56(v_66);
  return v_88;
}

float2 v_89(float v_90, float v_91, float2 v_92) {
  float v_93 = 0.0f;
  if (false) {
    float const v_94 = length(v_92);
    float const v_95 = atan2(-(v_92.y), (v_94 - v_92.x));
    v_93 = (2.0f * v_95);
  } else {
    float v_96 = 0.0f;
    if ((v_92.x != 0.0f)) {
      float const v_97 = atan2(-(v_92.y), -(v_92.x));
      v_96 = v_97;
    } else {
      float const v_98 = sign(v_92.y);
      v_96 = (v_98 * -1.57079637050628662109f);
    }
    v_93 = v_96;
  }
  float const v_99 = ((((v_93 * 0.15915493667125701904f) + 0.5f) + v_90) * v_91);
  return float2(v_99, 1.0f);
}

void v_100(thread float2* const v_101, float2x2 v_102, float2 v_103, float4 v_104, float4 v_105, float4 v_106) {
  float2 v_107 = (*v_101);
  v((&v_107), v_102, v_103, v_104.xy, float2(-1.0f), float2(v_105.x, v_106.x));
  (*v_101) = v_107;
  float2 v_108 = (*v_101);
  v((&v_108), v_102, v_103, v_104.zy, float2(1.0f, -1.0f), float2(v_105.y, v_106.y));
  (*v_101) = v_108;
  float2 v_109 = (*v_101);
  v((&v_109), v_102, v_103, v_104.zw, float2(1.0f), float2(v_105.z, v_106.z));
  (*v_101) = v_109;
  float2 v_110 = (*v_101);
  v((&v_110), v_102, v_103, v_104.xw, float2(-1.0f, 1.0f), float2(v_105.w, v_106.w));
  (*v_101) = v_110;
}

float4 v_111(float4 v_112, int v_113, int v_114) {
  float4 v_115 = v_112;
  if (bool(v_114)) {
    switch(v_113) {
      case 2:
      case 3:
      case 4:
      {
        float const v_116 = max(v_115.w, 0.00009999999747378752f);
        v_115 = float4((v_115.xyz / v_116), v_115.w);
        break;
      }
      case 5:
      case 6:
      case 7:
      case 9:
      case 10:
      {
        float const v_117 = max(v_115.w, 0.00009999999747378752f);
        v_115 = float4(v_115.x, (v_115.yz / v_117), v_115.w);
        break;
      }
      default:
      {
        break;
      }
    }
  }
  switch(v_113) {
    case 2:
    {
      float3 const v_118 = v_47(v_115.xyz);
      v_115 = float4(v_118, v_115.w);
      break;
    }
    case 3:
    {
      float3 const v_119 = v_56(v_115.xyz);
      v_115 = float4(v_119, v_115.w);
      break;
    }
    case 4:
    {
      float3 const v_120 = v_64(v_115.xyz);
      v_115 = float4(v_120, v_115.w);
      break;
    }
    case 5:
    {
      float3 const v_121 = v_29(v_115.xyz);
      float3 const v_122 = v_47(v_121);
      v_115 = float4(v_122, v_115.w);
      break;
    }
    case 6:
    {
      float3 const v_123 = v_29(v_115.xyz);
      float3 const v_124 = v_56(v_123);
      v_115 = float4(v_124, v_115.w);
      break;
    }
    case 7:
    {
      float3 const v_125 = v_29(v_115.xyz);
      float3 const v_126 = v_64(v_125);
      v_115 = float4(v_126, v_115.w);
      break;
    }
    case 9:
    {
      float3 const v_127 = v_35(v_115.xyz);
      v_115 = float4(v_127, v_115.w);
      break;
    }
    case 10:
    {
      float3 v_128 = v_115.xyz;
      float3 v_129 = 0.0f;
      v_128 = float3(v_128.x, (v_128.yz * 0.00999999977648258209f));
      if (((v_128.y + v_128.z) >= 1.0f)) {
        v_129 = float3((v_128.y / (v_128.y + v_128.z)));
      } else {
        float3 const v_130 = v_35(float3(v_128.x, 100.0f, 50.0f));
        v_129 = v_130;
        v_129 = (v_129 * ((1.0f - v_128.y) - v_128.z));
        v_129 = (v_129 + v_128.y);
      }
      v_115 = float4(v_129, v_115.w);
      break;
    }
    default:
    {
      break;
    }
  }
  return v_115;
}

float3 v_131(float3 v_132, float4 v_133, float3 v_134) {
  float3 const v_135 = (float(v_133.y) * v_132);
  float3 const v_136 = (v_135 + float(v_133.z));
  float3 const v_137 = powr(v_136, float3(float(v_133.x)));
  float3 const v_138 = (v_137 + float(v_134.y));
  float3 const v_139 = (float(v_133.w) * v_132);
  float3 const v_140 = (v_139 + float(v_134.z));
  float3 const v_141 = select(v_138, v_140, (v_132 < float3(float(v_134.x))));
  return v_141;
}

float2 v_142(int v_143, float2 v_144) {
  float2 v_145 = v_144;
  switch(v_143) {
    case 0:
    {
      float const v_146 = saturate(v_145.x);
      v_145.x = v_146;
      break;
    }
    case 1:
    {
      float const v_147 = fract(v_145.x);
      v_145.x = v_147;
      break;
    }
    case 2:
    {
      float const v_148 = (v_145.x - 1.0f);
      float const v_149 = floor((v_148 * 0.5f));
      v_145.x = ((v_148 - (2.0f * v_149)) - 1.0f);
      if (false) {
        float const v_150 = clamp(v_145.x, -1.0f, 1.0f);
        v_145.x = v_150;
      }
      float const v_151 = abs(v_145.x);
      v_145.x = v_151;
      break;
    }
    case 3:
    {
      bool v_152 = false;
      if ((v_145.x < 0.0f)) {
        v_152 = true;
      } else {
        v_152 = (v_145.x > 1.0f);
      }
      if (v_152) {
        return float2(0.0f, -1.0f);
      }
      break;
    }
    default:
    {
      break;
    }
  }
  return v_145;
}

float4 v_153(tint_array<float4, 4> v_154, float4 v_155, float2 v_156) {
  if ((v_156.y < 0.0f)) {
    return float4(0.0f);
  } else {
    if ((v_156.x <= v_155.x)) {
      return float4(v_154[0]);
    } else {
      if ((v_156.x < v_155.y)) {
        float4 const v_157 = mix(v_154[0], v_154[1], float4(((v_156.x - v_155.x) / (v_155.y - v_155.x))));
        return float4(v_157);
      } else {
        if ((v_156.x < v_155.z)) {
          float4 const v_158 = mix(v_154[1], v_154[2], float4(((v_156.x - v_155.y) / (v_155.z - v_155.y))));
          return float4(v_158);
        } else {
          if ((v_156.x < v_155.w)) {
            float4 const v_159 = mix(v_154[2], v_154[3], float4(((v_156.x - v_155.z) / (v_155.w - v_155.z))));
            return float4(v_159);
          } else {
            return float4(v_154[3]);
          }
        }
      }
    }
  }
  /* unreachable */
  return 0.0f;
}

float4 v_160(float4 v_161) {
  float const v_162 = max(v_161.w, 0.00009999999747378752f);
  return float4((v_161.xyz / v_162), v_161.w);
}

void v_163(tint_struct v_164, thread tint_struct_1* const v_165, tint_struct_2 v_166) {
  (*v_166.tint_member_27) = v_164.tint_member_1.y;
  float const v_167 = (*v_166.tint_member_10).tint_member_11[(*v_166.tint_member_27)].tint_member_15;
  float const v_168 = (*v_166.tint_member_10).tint_member_11[(*v_166.tint_member_27)].tint_member_16;
  float4x4 const v_169 = (*v_166.tint_member_10).tint_member_11[(*v_166.tint_member_27)].tint_member_12;
  float2 const v_170 = v_89(v_167, v_168, (v_169 * float4(v_164.tint_member_2, 0.0f, 1.0f)).xy);
  float2 v_171 = v_170;
  float2 const v_172 = v_142((*v_166.tint_member_10).tint_member_11[(*v_166.tint_member_27)].tint_member_17, v_171);
  v_171 = v_172;
  float4 const v_173 = v_153((*v_166.tint_member_10).tint_member_11[(*v_166.tint_member_27)].tint_member_13, (*v_166.tint_member_10).tint_member_11[(*v_166.tint_member_27)].tint_member_14, v_171);
  float4 const v_174 = v_173;
  float4 const v_175 = v_111(v_174, (*v_166.tint_member_10).tint_member_11[(*v_166.tint_member_27)].tint_member_18, (*v_166.tint_member_10).tint_member_11[(*v_166.tint_member_27)].tint_member_19);
  float4 v_176 = v_175;
  float4 const v_177 = (*v_166.tint_member_10).tint_member_11[(*v_166.tint_member_27)].tint_member_23;
  float4 const v_178 = (*v_166.tint_member_10).tint_member_11[(*v_166.tint_member_27)].tint_member_25;
  if ((v_177.w < 0.0f)) {
    float4 const v_179 = v_160(v_176);
    v_176 = v_179;
  } else {
    float const v_180 = (1.0f - v_177.w);
    float const v_181 = (v_177.w * v_178.w);
    float const v_182 = (v_177.w - v_181);
    float3 const v_183 = float3(v_176.wx, 1.0f);
    float const v_184 = dot(v_183, float3(v_180, v_182, v_181));
    v_176.w = v_184;
  }
  float3 v_185 = float3(v_176.xyz);
  float3 const v_186 = sign(v_185);
  float3 const v_187 = abs(v_185);
  float3 const v_188 = v_131(v_187, (*v_166.tint_member_10).tint_member_11[(*v_166.tint_member_27)].tint_member_22, v_177.xyz);
  v_185 = (v_186 * v_188);
  tint_array<tint_struct_5, 3> const v_189 = (*v_166.tint_member_10).tint_member_11[(*v_166.tint_member_27)].tint_member_20;
  float3x3 const v_190 = float3x3(float3x3(float3(v_189[0u].tint_member_21), float3(v_189[1u].tint_member_21), float3(v_189[2u].tint_member_21)));
  v_185 = (v_190 * v_185);
  float3 const v_191 = sign(v_185);
  float3 const v_192 = abs(v_185);
  float3 const v_193 = v_131(v_192, (*v_166.tint_member_10).tint_member_11[(*v_166.tint_member_27)].tint_member_24, v_178.xyz);
  v_185 = (v_191 * v_193);
  float const v_194 = v_178.w;
  float const v_195 = max(v_176.w, v_194);
  float3 const v_196 = (float3(v_185) * v_195);
  v_176 = float4(v_196, v_176.w);
  float const v_197 = (v_166.tint_member_29.sample(v_166.tint_member_28, (v_164.tint_member.xy * 0.125f), bias(clamp(-0.47499999403953552246f, -16.0f, 15.9899997711181640625f))).x - 0.5f);
  float3 const v_198 = (v_176.xyz + (v_197 * (*v_166.tint_member_10).tint_member_11[(*v_166.tint_member_27)].tint_member_26));
  float3 const v_199 = clamp(v_198, float3(0.0f), float3(v_176.w));
  float4 const v_200 = float4(v_199, v_176.w);
  float4 v_201 = float4(1.0f);
  float4 v_202 = 0.0f;
  if ((v_164.tint_member_8.x > 0.0f)) {
    v_202 = float4(1.0f);
  } else {
    if ((v_164.tint_member_8.y > 1.0f)) {
      float2 const v_203 = min(v_164.tint_member_4.xy, v_164.tint_member_4.zw);
      float2 const v_204 = v_203;
      float const v_205 = min(v_204.x, v_204.y);
      float const v_206 = (v_205 * v_164.tint_member.w);
      float const v_207 = ((v_164.tint_member_8.y - 1.0f) * v_164.tint_member.w);
      float const v_208 = (1.0f - (0.5f * v_207));
      float const v_209 = saturate((v_207 * (v_206 + v_208)));
      v_202 = float4(float(v_209));
    } else {
      float2 const v_210 = float2(v_164.tint_member_3.x, v_164.tint_member_3.y);
      float2x2 const v_211 = (float2x2(v_210, float2(v_164.tint_member_3.z, v_164.tint_member_3.w)) * (1.0f / v_164.tint_member.w));
      float2 const v_212 = (float2(1.0f, 0.0f) * v_211);
      float2 const v_213 = (float2(0.0f, 1.0f) * v_211);
      float const v_214 = dot(v_212, v_212);
      float const v_215 = rsqrt(v_214);
      float const v_216 = dot(v_213, v_213);
      float const v_217 = rsqrt(v_216);
      float2 const v_218 = float2(v_215, v_217);
      float2 const v_219 = min(v_164.tint_member_4.xy, v_164.tint_member_4.zw);
      float2 const v_220 = (v_218 * (v_164.tint_member_7.x + v_219));
      float const v_221 = min(v_220.x, v_220.y);
      float2 v_222 = float2(v_221, -1.0f);
      float v_223 = 0.0f;
      float v_224 = 0.0f;
      if ((v_164.tint_member_8.x > -0.94999998807907104492f)) {
        float2 const v_225 = (v_218 * ((v_164.tint_member_4.xy + v_164.tint_member_4.zw) + (2.0f * v_164.tint_member_7.xx)));
        float const v_226 = min(v_225.x, v_225.y);
        float const v_227 = min(v_226, 1.0f);
        v_223 = v_227;
        v_224 = (1.0f - (0.5f * v_223));
      } else {
        float2 const v_228 = ((2.0f * v_164.tint_member_7.x) * v_218);
        float2 const v_229 = (v_228 - v_220);
        float const v_230 = max(v_229.x, v_229.y);
        v_222.y = -(v_230);
        if ((v_164.tint_member_7.x > 0.0f)) {
          float const v_231 = min(v_228.x, v_228.y);
          float const v_232 = v_231;
          float2 const v_233 = select(float2(v_232), v_228, (v_229 >= float2(-0.5f)));
          float2 const v_234 = v_233;
          float const v_235 = max(v_234.x, v_234.y);
          float const v_236 = saturate(v_235);
          v_223 = v_236;
          v_224 = (1.0f - (0.5f * v_223));
        } else {
          v_224 = 1.0f;
          v_223 = v_224;
        }
      }
      float2 v_237 = v_222;
      v_100((&v_237), v_211, v_164.tint_member_7, v_164.tint_member_4, v_164.tint_member_5, v_164.tint_member_6);
      v_222 = v_237;
      float const v_238 = min(v_164.tint_member_8.y, 0.0f);
      float const v_239 = (v_238 * v_164.tint_member.w);
      float const v_240 = min((v_222.x + v_239), -(v_222.y));
      float const v_241 = (v_223 * (v_240 + v_224));
      float const v_242 = saturate(v_241);
      v_202 = float4(float(v_242));
    }
  }
  v_201 = v_202;
  (*v_165).tint_member_9 = (v_200 * v_201);
}

tint_struct_1 v_243(tint_struct v_244, tint_struct_2 v_245) {
  tint_struct_1 v_246 = {};
  v_163(v_244, (&v_246), v_245);
  return v_246;
}

fragment tint_struct_6 dawn_entry_point(float4 v_248 [[position]], tint_struct_7 v_249 [[stage_in]], const device tint_struct_3* v_250 [[buffer(2)]], sampler v_251 [[sampler(0)]], texture2d<float, access::sample> v_252 [[texture(0)]]) {
  thread uint v_253 = 0u;
  tint_struct_2 const v_254 = tint_struct_2{.tint_member_10=v_250, .tint_member_27=(&v_253), .tint_member_28=v_251, .tint_member_29=v_252};
  tint_struct_6 v_255 = {};
  v_255.tint_member_30 = v_243(tint_struct{.tint_member=v_248, .tint_member_1=v_249.tint_member_31, .tint_member_2=v_249.tint_member_32, .tint_member_3=v_249.tint_member_33, .tint_member_4=v_249.tint_member_34, .tint_member_5=v_249.tint_member_35, .tint_member_6=v_249.tint_member_36, .tint_member_7=v_249.tint_member_37, .tint_member_8=v_249.tint_member_38}, v_254).tint_member_9;
  return v_255;
}
       dawn_entry_point                      