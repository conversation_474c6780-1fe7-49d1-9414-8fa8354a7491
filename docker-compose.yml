version: '3.8'

services:
  # 后端服务
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    container_name: panel-backend
    ports:
      - "8080:8080"
    volumes:
      - ./data:/app/data
      - ./backend/configs:/app/configs
    environment:
      - GIN_MODE=release
    restart: unless-stopped
    networks:
      - panel-network

  # 前端服务
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
    container_name: panel-frontend
    ports:
      - "3000:80"
    depends_on:
      - backend
    restart: unless-stopped
    networks:
      - panel-network

  # Nginx反向代理
  nginx:
    image: nginx:alpine
    container_name: panel-nginx
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf
      - ./nginx/conf.d:/etc/nginx/conf.d
      - ./ssl:/etc/nginx/ssl
    depends_on:
      - backend
      - frontend
    restart: unless-stopped
    networks:
      - panel-network

networks:
  panel-network:
    driver: bridge

volumes:
  panel-data:
    driver: local
