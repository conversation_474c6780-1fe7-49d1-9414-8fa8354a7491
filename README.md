# Linux运维管理面板系统

一个基于Go + Hertz + Vue3 + TDesign的现代化Linux运维管理面板系统。

## 🚀 项目特性

- **现代化技术栈**：Go + Hertz + Vue3 + TDesign
- **容器化部署**：Docker + Docker Compose
- **完整功能模块**：系统监控、容器管理、数据库管理、文件管理等
- **安全可靠**：JWT认证、RBAC权限控制、多层安全防护
- **高性能**：基于Hertz高性能HTTP框架
- **易于扩展**：模块化架构设计

## 📁 项目结构

```
panel/
├── backend/                  # 后端项目 (Go + Hertz)
├── frontend/                 # 前端项目 (Vue3 + TDesign)
├── docker/                   # Docker相关文件
├── scripts/                  # 部署和运维脚本
├── linux-panel-docs/         # 项目设计文档
├── docker-compose.yml        # Docker Compose配置
└── README.md                 # 项目说明
```

## 🛠 技术栈

### 后端技术栈
- **编程语言**: Go
- **HTTP框架**: Hertz (字节跳动开源的高性能HTTP框架)
- **ORM框架**: GORM
- **数据库**: SQLite
- **依赖注入**: Wire

### 前端技术栈
- **前端框架**: Vue 3 + Composition API
- **构建工具**: Vite
- **UI组件库**: TDesign-Vue (腾讯开源的企业级设计系统)
- **状态管理**: Pinia
- **类型支持**: TypeScript

## 🚀 快速开始

### 环境要求
- Go 1.21+
- Node.js 18+
- Docker & Docker Compose

### 开发环境启动

1. **克隆项目**
```bash
git clone https://git.yunzmall.com/dev-ops/panel.git
cd panel
```

2. **启动后端服务**
```bash
cd backend
go mod tidy
go run cmd/server/main.go
```

3. **启动前端服务**
```bash
cd frontend
npm install
npm run dev
```

### 生产环境部署

```bash
# 使用Docker Compose一键部署
docker-compose up -d
```

## 📖 功能模块

- **🔐 认证授权**: 用户登录、JWT认证、RBAC权限控制
- **📊 系统监控**: 实时系统资源监控、性能指标展示
- **🐳 容器管理**: Docker容器、镜像、网络、卷管理
- **🗄️ 数据库管理**: MySQL、PostgreSQL、Redis等数据库管理
- **📁 文件管理**: 文件上传下载、目录管理、权限控制
- **🌐 网站管理**: 域名管理、SSL证书、反向代理配置
- **📦 应用商店**: 一键安装常用应用和服务
- **⚙️ 系统设置**: 系统配置、用户管理、安全设置

## 📚 文档

详细的设计文档请查看 [linux-panel-docs](./linux-panel-docs/) 目录：

- [系统架构设计](./linux-panel-docs/system_architecture_design.md)
- [项目目录结构](./linux-panel-docs/project_directory_structure.md)
- [API设计规范](./linux-panel-docs/api_design_spec.md)
- [数据库设计](./linux-panel-docs/database_schema.sql)

## 🤝 贡献指南

1. Fork 本仓库
2. 创建特性分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开 Pull Request

## 📄 开源协议

本项目采用 MIT 协议 - 查看 [LICENSE](LICENSE) 文件了解详情

## 📞 联系我们

如有问题或建议，请通过以下方式联系：

- 提交 Issue
- 发送邮件
- 加入讨论群
